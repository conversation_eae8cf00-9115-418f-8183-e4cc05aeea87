{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams}from'react-router-dom';import{Container,Box,Tabs,Tab,CircularProgress,Alert}from'@mui/material';import Iconify from'components/Iconify/index';import{mainYellowColor,APIURL}from\"helpers/constants\";import{successSnackbar,errorSnackbar}from'components/Snackbar/index';import{toast}from'react-toastify';import{getHeaders}from\"helpers/functions\";// Components\nimport Header from'./components/Header';import Description from'./components/Description';import Statistics from'./components/Statistics';import Progress from'./components/Progress';import Members from'./components/Members';import MilestoneList from'./components/MilestoneList';import TaskList from'./components/TaskList';import MilestoneOverview from'./components/MilestoneOverview';import AccessManagement from'./components/AccessManagement';// Hooks\nimport usePlanData from'./hooks/usePlanData';import useViewMode from'./hooks/useViewMode';// Dialogs\nimport InviteDialog from'./dialogs/InviteDialog';import DeleteDialog from'./dialogs/DeleteDialog';import OptOutDialog from'./dialogs/OptOutDialog';import ConfirmDialog from'./dialogs/ConfirmDialog';// Services\nimport{updateMilestone,updateTask,updateSubtask,addTask,addSubtask,deleteTask,deleteSubtask,assignMembersToTask}from'../services';// Styles\nimport styles from'./styles.module.scss';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PlanDetail=()=>{var _planInfo$user_access,_planInfo$user_access2;const{param}=useParams();const[activeTab,setActiveTab]=useState(()=>{// Get active tab value from localStorage, default to 'overview' if not found\nreturn localStorage.getItem(`plan_${param}_activeTab`)||'overview';});const[plan,setPlan]=useState(null);const[dialogState,setDialogState]=useState({invite:false,delete:false,optOut:false,deleteTask:false,deleteSubtask:false});const[selectedTaskToDelete]=useState(null);const[selectedSubtaskToDelete]=useState(null);// Custom hooks\nconst{planInfo,loading,error,invitedUsers,handleDeletePlan,handleOptOutPlan,handleInviteUser,calculatePlanStats,calculateSubtaskProgress,getSubtaskStatus,calculateTaskProgress,getTaskStatus,calculateMilestoneProgress,getMilestoneStatus,fetchInvitedUsers}=usePlanData(param);const{viewMode,handleViewModeChange}=useViewMode();// Update plan state when planInfo changes\nuseEffect(()=>{if(planInfo){setPlan(planInfo);}},[planInfo]);// Remove active tab from localStorage when component unmounts\nuseEffect(()=>{return()=>{localStorage.removeItem(`plan_${param}_activeTab`);};},[param]);// Calculate plan statistics\nconst stats=calculatePlanStats?calculatePlanStats(planInfo):null;// Dialog handlers\nconst openDialog=dialogName=>{setDialogState(prev=>({...prev,[dialogName]:true}));};const closeDialog=dialogName=>{setDialogState(prev=>({...prev,[dialogName]:false}));};// Tab change handler\nconst handleTabChange=(_,newValue)=>{setActiveTab(newValue);// Save active tab to localStorage\nlocalStorage.setItem(`plan_${param}_activeTab`,newValue);};// Refresh plan data\nconst refreshPlanData=async()=>{try{window.location.reload();// Simple refresh for now\n}catch(error){console.error('Error refreshing plan data:',error);}};// Access management handlers\nconst handleAddAccess=async(email,accessLevel)=>{try{const response=await fetch(`${APIURL}/api/plans/${param}/access`,{method:'POST',headers:{...getHeaders(),'Content-Type':'application/json'},body:JSON.stringify({email,access_level:accessLevel})});if(!response.ok){const error=await response.json();throw new Error(error.error||'Failed to add access');}// Refresh plan data\nawait refreshPlanData();toast.success('Access granted successfully');}catch(error){console.error('Error adding access:',error);toast.error(error.message||'Failed to add access');throw error;}};const handleUpdateAccess=async function(accessId,accessLevel){let isHeadOwner=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;try{const response=await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`,{method:'PUT',headers:{...getHeaders(),'Content-Type':'application/json'},body:JSON.stringify({access_level:accessLevel,is_head_owner:isHeadOwner})});if(!response.ok){const error=await response.json();throw new Error(error.error||'Failed to update access');}// Refresh plan data\nawait refreshPlanData();toast.success('Access updated successfully');}catch(error){console.error('Error updating access:',error);toast.error(error.message||'Failed to update access');throw error;}};const handleRemoveAccess=async accessId=>{try{const response=await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`,{method:'DELETE',headers:getHeaders()});if(!response.ok){const error=await response.json();throw new Error(error.error||'Failed to remove access');}// Refresh plan data\nawait refreshPlanData();toast.success('Access removed successfully');}catch(error){console.error('Error removing access:',error);toast.error(error.message||'Failed to remove access');throw error;}};// Handle milestone update\nconst handleUpdateMilestone=async updatedMilestone=>{try{console.log('Updating milestone:',updatedMilestone);await updateMilestone(updatedMilestone);// Update local state\nconst updatedPlan={...plan};const milestoneIndex=updatedPlan.milestones.findIndex(m=>m.id===updatedMilestone.id);if(milestoneIndex!==-1){updatedPlan.milestones[milestoneIndex]={...updatedPlan.milestones[milestoneIndex],...updatedMilestone};setPlan(updatedPlan);}successSnackbar('Milestone updated successfully');}catch(error){console.error('Error updating milestone:',error);errorSnackbar('Failed to update milestone');}};// Handle task update\nconst handleUpdateTask=async updatedTask=>{try{console.log('Updating task:',updatedTask);await updateTask(updatedTask);// Update local state\nconst updatedPlan={...plan};const milestoneIndex=updatedPlan.milestones.findIndex(m=>m.tasks&&m.tasks.some(t=>t.id===updatedTask.id));if(milestoneIndex!==-1){const taskIndex=updatedPlan.milestones[milestoneIndex].tasks.findIndex(t=>t.id===updatedTask.id);if(taskIndex!==-1){updatedPlan.milestones[milestoneIndex].tasks[taskIndex]={...updatedPlan.milestones[milestoneIndex].tasks[taskIndex],...updatedTask};setPlan(updatedPlan);}}successSnackbar('Task updated successfully');}catch(error){console.error('Error updating task:',error);errorSnackbar('Failed to update task');}};// Handle subtask update\nconst handleUpdateSubtask=async updatedSubtask=>{try{console.log('Updating subtask:',updatedSubtask);// Call API to update subtask\nawait updateSubtask(updatedSubtask);// Create a copy of current plan to update\nconst updatedPlan={...plan};// Find the task containing the subtask\nlet taskFound=false;// Update subtask in state\nfor(let i=0;i<updatedPlan.milestones.length;i++){const milestone=updatedPlan.milestones[i];if(!milestone.tasks)continue;for(let j=0;j<milestone.tasks.length;j++){const task=milestone.tasks[j];if(!task.subtasks)continue;// Update subtask in task\nconst subtaskIndex=task.subtasks.findIndex(s=>s.id===updatedSubtask.id);if(subtaskIndex!==-1){// Update subtask\ntask.subtasks[subtaskIndex]={...task.subtasks[subtaskIndex],...updatedSubtask};taskFound=true;break;}}if(taskFound)break;}// Update state with new plan\nsetPlan(updatedPlan);successSnackbar('Subtask updated successfully');}catch(error){console.error('Error updating subtask:',error);errorSnackbar('Failed to update subtask');}};// Handle add task\nconst handleAddTask=async newTask=>{try{console.log('Adding new task:',newTask);const response=await addTask(newTask);console.log('Add task response:',response);// Update local state\nconst updatedPlan={...plan};// Find milestone to add new task\nconst milestoneIndex=updatedPlan.milestones.findIndex(m=>m.id===newTask.milestone);if(milestoneIndex!==-1){// Add new task to milestone\nif(!updatedPlan.milestones[milestoneIndex].tasks){updatedPlan.milestones[milestoneIndex].tasks=[];}// Add the new task with data from response\nconst taskToAdd=response.data||{...newTask,id:Date.now(),// Temporary ID if response doesn't provide one\nsubtasks:[]};updatedPlan.milestones[milestoneIndex].tasks.push(taskToAdd);setPlan(updatedPlan);}successSnackbar('Task added successfully');}catch(error){console.error('Error adding task:',error);errorSnackbar('Failed to add task');}};// Handle add subtask\nconst handleAddSubtask=async newSubtask=>{try{console.log('Adding new subtask:',newSubtask);const response=await addSubtask(newSubtask);console.log('Add subtask response:',response);// Update local state\nconst updatedPlan={...plan};// Find task to add new subtask\nlet taskFound=false;for(let i=0;i<updatedPlan.milestones.length;i++){const milestone=updatedPlan.milestones[i];if(!milestone.tasks)continue;for(let j=0;j<milestone.tasks.length;j++){const task=milestone.tasks[j];if(task.slug===newSubtask.task){// Add new subtask to task\nif(!task.subtasks){task.subtasks=[];}// Add the new subtask with data from response\nconst subtaskToAdd=response.data||{...newSubtask,id:Date.now()// Temporary ID if response doesn't provide one\n};task.subtasks.push(subtaskToAdd);taskFound=true;break;}}if(taskFound)break;}setPlan(updatedPlan);successSnackbar('Subtask added successfully');}catch(error){console.error('Error adding subtask:',error);errorSnackbar('Failed to add subtask');}};// Handle delete task\nconst handleDeleteTask=async taskToDelete=>{try{console.log('Deleting task:',taskToDelete);await deleteTask(taskToDelete.slug);// Update local state\nconst updatedPlan={...plan};// Find milestone containing the task to delete\nconst milestoneIndex=updatedPlan.milestones.findIndex(m=>m.tasks&&m.tasks.some(t=>t.id===taskToDelete.id));if(milestoneIndex!==-1){// Filter out the task to delete\nupdatedPlan.milestones[milestoneIndex].tasks=updatedPlan.milestones[milestoneIndex].tasks.filter(t=>t.id!==taskToDelete.id);setPlan(updatedPlan);}successSnackbar('Task deleted successfully');}catch(error){console.error('Error deleting task:',error);errorSnackbar('Failed to delete task');}};// Handle delete subtask\nconst handleDeleteSubtask=async subtaskToDelete=>{try{console.log('Deleting subtask:',subtaskToDelete);await deleteSubtask(subtaskToDelete.slug);// Update local state\nconst updatedPlan={...plan};// Find task containing the subtask to delete\nlet taskFound=false;for(let i=0;i<updatedPlan.milestones.length;i++){const milestone=updatedPlan.milestones[i];if(!milestone.tasks)continue;for(let j=0;j<milestone.tasks.length;j++){const task=milestone.tasks[j];if(!task.subtasks)continue;// Filter out the subtask to delete\nconst originalLength=task.subtasks.length;task.subtasks=task.subtasks.filter(s=>s.id!==subtaskToDelete.id);if(task.subtasks.length<originalLength){taskFound=true;break;}}if(taskFound)break;}setPlan(updatedPlan);toast.success('Subtask deleted successfully');}catch(error){console.error('Error deleting subtask:',error);toast.error('Failed to delete subtask');}};// Handle assign members to task\nconst handleAssignMembers=async(taskToAssign,memberIds)=>{try{console.log('Assigning members to task:',taskToAssign,memberIds);await assignMembersToTask(taskToAssign.slug,memberIds);// Update local state\nconst updatedPlan={...plan};// Find the task to update\nlet taskFound=false;for(let i=0;i<updatedPlan.milestones.length;i++){const milestone=updatedPlan.milestones[i];if(!milestone.tasks)continue;for(let j=0;j<milestone.tasks.length;j++){const task=milestone.tasks[j];if(task.id===taskToAssign.id){// Update assignees\n// Find user objects for the selected member IDs\nconst assignedMembers=memberIds.map(memberId=>{var _planInfo$invited_use;// Check if it's the plan owner\nif(planInfo.owner&&planInfo.owner.id===memberId){return{id:planInfo.owner.id,first_name:planInfo.owner.first_name,last_name:planInfo.owner.last_name,email:planInfo.owner.email,avatar:planInfo.owner.avatar};}// Check in invited users\nconst invitedUser=(_planInfo$invited_use=planInfo.invited_users)===null||_planInfo$invited_use===void 0?void 0:_planInfo$invited_use.find(user=>user.invited_user_info&&user.invited_user_info.id===memberId);if(invitedUser){return{id:invitedUser.invited_user_info.id,first_name:invitedUser.invited_user_info.first_name,last_name:invitedUser.invited_user_info.last_name,email:invitedUser.email,avatar:invitedUser.invited_user_info.avatar};}return null;}).filter(member=>member!==null);task.assignees=assignedMembers;taskFound=true;break;}}if(taskFound)break;}setPlan(updatedPlan);toast.success('Members assigned successfully');}catch(error){console.error('Error assigning members to task:',error);toast.error('Failed to assign members to task');}};if(error){return/*#__PURE__*/_jsx(Container,{className:styles.container,children:/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mt:4},children:\"An error occurred while loading plan information. Please try again later.\"})});}return/*#__PURE__*/_jsxs(Container,{maxWidth:\"lg\",className:styles.container,sx:{padding:'20px',minHeight:'calc(100vh - 65px)',fontFamily:'\"Recursive Variable\", sans-serif'},children:[loading?/*#__PURE__*/_jsx(Box,{className:styles.loadingContainer,children:/*#__PURE__*/_jsx(CircularProgress,{size:60,sx:{color:mainYellowColor}})}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Header,{planInfo:planInfo,viewMode:viewMode,onViewModeChange:handleViewModeChange,onOpenInviteDialog:()=>openDialog('invite'),onOpenDeleteDialog:()=>openDialog('delete'),onOpenOptOutDialog:()=>openDialog('optOut')}),/*#__PURE__*/_jsx(Box,{sx:{borderBottom:1,borderColor:'divider',mb:0.5,mt:-1},children:/*#__PURE__*/_jsxs(Tabs,{value:activeTab,onChange:handleTabChange,variant:\"scrollable\",scrollButtons:\"auto\",sx:{minHeight:'36px','& .MuiTab-root':{textTransform:'none',fontWeight:600,fontSize:'0.9rem',minWidth:'auto',minHeight:'36px',px:2,py:0.5,fontFamily:'\"Recursive Variable\", sans-serif'},'& .Mui-selected':{color:`${mainYellowColor} !important`},'& .MuiTabs-indicator':{backgroundColor:mainYellowColor,height:'2px'}},children:[/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:dashboard\",width:16,height:16}),iconPosition:\"start\",label:\"Overview\",value:\"overview\",sx:{gap:'4px'}}),/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:flag\",width:16,height:16}),iconPosition:\"start\",label:\"Milestones\",value:\"milestones\",sx:{gap:'4px'}}),/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:task\",width:16,height:16}),iconPosition:\"start\",label:\"Tasks\",value:\"tasks\",sx:{gap:'4px'}}),/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:people\",width:16,height:16}),iconPosition:\"start\",label:\"Members\",value:\"members\",sx:{gap:'4px'}}),(planInfo===null||planInfo===void 0?void 0:(_planInfo$user_access=planInfo.user_access_level)===null||_planInfo$user_access===void 0?void 0:_planInfo$user_access.access_level)==='owner'&&/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:security\",width:16,height:16}),iconPosition:\"start\",label:\"Access\",value:\"access\",sx:{gap:'4px'}})]})}),/*#__PURE__*/_jsxs(Box,{className:styles.tabContent,children:[activeTab==='overview'&&/*#__PURE__*/_jsxs(Box,{className:styles.overviewTab,sx:{gap:0.5},children:[/*#__PURE__*/_jsx(Description,{planInfo:planInfo}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:{xs:'column',md:'row'},gap:1,mb:0.5},children:[/*#__PURE__*/_jsx(Statistics,{stats:stats}),/*#__PURE__*/_jsx(Progress,{stats:stats})]}),/*#__PURE__*/_jsx(Members,{invitedUsers:invitedUsers,planOwner:planInfo===null||planInfo===void 0?void 0:planInfo.user,onInvite:()=>openDialog('invite')}),/*#__PURE__*/_jsx(MilestoneOverview,{milestones:planInfo===null||planInfo===void 0?void 0:planInfo.milestones,calculateMilestoneProgress:calculateMilestoneProgress,getMilestoneStatus:getMilestoneStatus,calculateTaskProgress:calculateTaskProgress,getTaskStatus:getTaskStatus,calculateSubtaskProgress:calculateSubtaskProgress,getSubtaskStatus:getSubtaskStatus})]}),activeTab==='milestones'&&/*#__PURE__*/_jsx(MilestoneList,{milestones:planInfo===null||planInfo===void 0?void 0:planInfo.milestones,viewMode:viewMode,compact:false,showSubtasks:true,calculateMilestoneProgress:calculateMilestoneProgress,getMilestoneStatus:getMilestoneStatus,calculateTaskProgress:calculateTaskProgress,getTaskStatus:getTaskStatus,calculateSubtaskProgress:calculateSubtaskProgress,getSubtaskStatus:getSubtaskStatus,onUpdateMilestone:handleUpdateMilestone,onUpdateTask:handleUpdateTask,onUpdateSubtask:handleUpdateSubtask,onAddTask:handleAddTask,onAddSubtask:handleAddSubtask,onDeleteTask:handleDeleteTask,onDeleteSubtask:handleDeleteSubtask,onAssignMembers:handleAssignMembers,invitedUsers:(planInfo===null||planInfo===void 0?void 0:planInfo.invited_users)||[],planOwner:planInfo===null||planInfo===void 0?void 0:planInfo.owner}),activeTab==='tasks'&&/*#__PURE__*/_jsx(TaskList,{milestones:planInfo===null||planInfo===void 0?void 0:planInfo.milestones,onUpdateTask:handleUpdateTask,onUpdateSubtask:handleUpdateSubtask,onAddSubtask:handleAddSubtask,onDeleteTask:handleDeleteTask,onDeleteSubtask:handleDeleteSubtask}),activeTab==='members'&&/*#__PURE__*/_jsx(Members,{invitedUsers:invitedUsers,planOwner:planInfo===null||planInfo===void 0?void 0:planInfo.user,onInvite:()=>openDialog('invite'),expanded:true}),activeTab==='access'&&(planInfo===null||planInfo===void 0?void 0:(_planInfo$user_access2=planInfo.user_access_level)===null||_planInfo$user_access2===void 0?void 0:_planInfo$user_access2.access_level)==='owner'&&/*#__PURE__*/_jsx(AccessManagement,{planInfo:planInfo,userAccessLevel:planInfo===null||planInfo===void 0?void 0:planInfo.user_access_level,onAddAccess:handleAddAccess,onUpdateAccess:handleUpdateAccess,onRemoveAccess:handleRemoveAccess})]})]}),/*#__PURE__*/_jsx(InviteDialog,{open:dialogState.invite,onClose:()=>closeDialog('invite'),onInvite:handleInviteUser,planInfo:planInfo}),/*#__PURE__*/_jsx(DeleteDialog,{open:dialogState.delete,onClose:()=>closeDialog('delete'),onDelete:handleDeletePlan}),/*#__PURE__*/_jsx(OptOutDialog,{open:dialogState.optOut,onClose:()=>closeDialog('optOut'),onOptOut:handleOptOutPlan}),/*#__PURE__*/_jsx(ConfirmDialog,{open:dialogState.deleteTask,onClose:()=>setDialogState(prev=>({...prev,deleteTask:false})),onConfirm:()=>handleDeleteTask(selectedTaskToDelete),title:\"Delete Task\",description:`Are you sure you want to delete the task \"${selectedTaskToDelete===null||selectedTaskToDelete===void 0?void 0:selectedTaskToDelete.name}\"? This action cannot be undone.`}),/*#__PURE__*/_jsx(ConfirmDialog,{open:dialogState.deleteSubtask,onClose:()=>setDialogState(prev=>({...prev,deleteSubtask:false})),onConfirm:()=>handleDeleteSubtask(selectedSubtaskToDelete),title:\"Delete Subtask\",description:`Are you sure you want to delete the subtask \"${selectedSubtaskToDelete===null||selectedSubtaskToDelete===void 0?void 0:selectedSubtaskToDelete.name}\"? This action cannot be undone.`})]});};export default PlanDetail;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Container", "Box", "Tabs", "Tab", "CircularProgress", "<PERSON><PERSON>", "Iconify", "mainYellowColor", "APIURL", "successSnackbar", "errorSnackbar", "toast", "getHeaders", "Header", "Description", "Statistics", "Progress", "Members", "MilestoneList", "TaskList", "MilestoneOverview", "AccessManagement", "usePlanData", "useViewMode", "InviteDialog", "DeleteDialog", "OptOutDialog", "ConfirmDialog", "updateMilestone", "updateTask", "updateSubtask", "addTask", "addSubtask", "deleteTask", "deleteSubtask", "assignMembersToTask", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "PlanDetail", "_planInfo$user_access", "_planInfo$user_access2", "param", "activeTab", "setActiveTab", "localStorage", "getItem", "plan", "setPlan", "dialogState", "setDialogState", "invite", "delete", "optOut", "selectedTaskToDelete", "selectedSubtaskToDelete", "planInfo", "loading", "error", "invitedUsers", "handleDeletePlan", "handleOptOutPlan", "handleInviteUser", "calculatePlanStats", "calculateSubtaskProgress", "getSubtaskStatus", "calculateTaskProgress", "getTaskStatus", "calculateMilestoneProgress", "getMilestoneStatus", "fetchInvitedUsers", "viewMode", "handleViewModeChange", "removeItem", "stats", "openDialog", "dialogName", "prev", "closeDialog", "handleTabChange", "_", "newValue", "setItem", "refreshPlanData", "window", "location", "reload", "console", "handleAddAccess", "email", "accessLevel", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "access_level", "ok", "json", "Error", "success", "message", "handleUpdateAccess", "accessId", "isHeadOwner", "arguments", "length", "undefined", "is_head_owner", "handleRemoveAccess", "handleUpdateMilestone", "updatedMilestone", "log", "updatedPlan", "milestoneIndex", "milestones", "findIndex", "m", "id", "handleUpdateTask", "updatedTask", "tasks", "some", "t", "taskIndex", "handleUpdateSubtask", "updatedSubtask", "taskFound", "i", "milestone", "j", "task", "subtasks", "subtaskIndex", "s", "handleAddTask", "newTask", "taskToAdd", "data", "Date", "now", "push", "handleAddSubtask", "newSubtask", "slug", "subtaskToAdd", "handleDeleteTask", "taskToDelete", "filter", "handleDeleteSubtask", "subtaskToDelete", "original<PERSON>ength", "handleAssignMembers", "taskToAssign", "memberIds", "assignedMembers", "map", "memberId", "_planInfo$invited_use", "owner", "first_name", "last_name", "avatar", "invitedUser", "invited_users", "find", "user", "invited_user_info", "member", "assignees", "className", "container", "children", "severity", "sx", "mt", "max<PERSON><PERSON><PERSON>", "padding", "minHeight", "fontFamily", "loadingContainer", "size", "color", "onViewModeChange", "onOpenInviteDialog", "onOpenDeleteDialog", "onOpenOptOutDialog", "borderBottom", "borderColor", "mb", "value", "onChange", "variant", "scrollButtons", "textTransform", "fontWeight", "fontSize", "min<PERSON><PERSON><PERSON>", "px", "py", "backgroundColor", "height", "icon", "width", "iconPosition", "label", "gap", "user_access_level", "tab<PERSON>ontent", "overviewTab", "display", "flexDirection", "xs", "md", "<PERSON><PERSON><PERSON><PERSON>", "onInvite", "compact", "showSubtasks", "onUpdateMilestone", "onUpdateTask", "onUpdateSubtask", "onAddTask", "onAddSubtask", "onDeleteTask", "onDeleteSubtask", "onAssignMembers", "expanded", "userAccessLevel", "onAddAccess", "onUpdateAccess", "onRemoveAccess", "open", "onClose", "onDelete", "onOptOut", "onConfirm", "title", "description", "name"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useParams } from 'react-router-dom';\r\nimport { Container, Box, Tabs, Tab, CircularProgress, Alert } from '@mui/material';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\r\nimport { successSnackbar, errorSnackbar } from 'components/Snackbar/index';\r\nimport { toast } from 'react-toastify';\r\nimport { getHeaders } from \"helpers/functions\";\r\n\r\n// Components\r\nimport Header from './components/Header';\r\nimport Description from './components/Description';\r\nimport Statistics from './components/Statistics';\r\nimport Progress from './components/Progress';\r\nimport Members from './components/Members';\r\nimport MilestoneList from './components/MilestoneList';\r\nimport TaskList from './components/TaskList';\r\nimport MilestoneOverview from './components/MilestoneOverview';\r\nimport AccessManagement from './components/AccessManagement';\r\n\r\n// Hooks\r\nimport usePlanData from './hooks/usePlanData';\r\nimport useViewMode from './hooks/useViewMode';\r\n\r\n// Dialogs\r\nimport InviteDialog from './dialogs/InviteDialog';\r\nimport DeleteDialog from './dialogs/DeleteDialog';\r\nimport OptOutDialog from './dialogs/OptOutDialog';\r\nimport ConfirmDialog from './dialogs/ConfirmDialog';\r\n\r\n// Services\r\nimport {\r\n  updateMilestone,\r\n  updateTask,\r\n  updateSubtask,\r\n  addTask,\r\n  addSubtask,\r\n  deleteTask,\r\n  deleteSubtask,\r\n  assignMembersToTask\r\n} from '../services';\r\n\r\n// Styles\r\nimport styles from './styles.module.scss';\r\n\r\nconst PlanDetail = () => {\r\n  const { param } = useParams();\r\n  const [activeTab, setActiveTab] = useState(() => {\r\n    // Get active tab value from localStorage, default to 'overview' if not found\r\n    return localStorage.getItem(`plan_${param}_activeTab`) || 'overview';\r\n  });\r\n  const [plan, setPlan] = useState(null);\r\n  const [dialogState, setDialogState] = useState({\r\n    invite: false,\r\n    delete: false,\r\n    optOut: false,\r\n    deleteTask: false,\r\n    deleteSubtask: false\r\n  });\r\n  const [selectedTaskToDelete, ] = useState(null);\r\n  const [selectedSubtaskToDelete, ] = useState(null);\r\n\r\n  // Custom hooks\r\n  const {\r\n    planInfo,\r\n    loading,\r\n    error,\r\n    invitedUsers,\r\n    handleDeletePlan,\r\n    handleOptOutPlan,\r\n    handleInviteUser,\r\n    calculatePlanStats,\r\n    calculateSubtaskProgress,\r\n    getSubtaskStatus,\r\n    calculateTaskProgress,\r\n    getTaskStatus,\r\n    calculateMilestoneProgress,\r\n    getMilestoneStatus,\r\n    fetchInvitedUsers\r\n  } = usePlanData(param);\r\n\r\n  const {\r\n    viewMode,\r\n    handleViewModeChange\r\n  } = useViewMode();\r\n\r\n  // Update plan state when planInfo changes\r\n  useEffect(() => {\r\n    if (planInfo) {\r\n      setPlan(planInfo);\r\n    }\r\n  }, [planInfo]);\r\n\r\n  // Remove active tab from localStorage when component unmounts\r\n  useEffect(() => {\r\n    return () => {\r\n      localStorage.removeItem(`plan_${param}_activeTab`);\r\n    };\r\n  }, [param]);\r\n\r\n  // Calculate plan statistics\r\n  const stats = calculatePlanStats ? calculatePlanStats(planInfo) : null;\r\n\r\n  // Dialog handlers\r\n  const openDialog = (dialogName) => {\r\n    setDialogState(prev => ({ ...prev, [dialogName]: true }));\r\n  };\r\n\r\n  const closeDialog = (dialogName) => {\r\n    setDialogState(prev => ({ ...prev, [dialogName]: false }));\r\n  };\r\n\r\n  // Tab change handler\r\n  const handleTabChange = (_, newValue) => {\r\n    setActiveTab(newValue);\r\n    // Save active tab to localStorage\r\n    localStorage.setItem(`plan_${param}_activeTab`, newValue);\r\n  };\r\n\r\n  // Refresh plan data\r\n  const refreshPlanData = async () => {\r\n    try {\r\n      window.location.reload(); // Simple refresh for now\r\n    } catch (error) {\r\n      console.error('Error refreshing plan data:', error);\r\n    }\r\n  };\r\n\r\n  // Access management handlers\r\n  const handleAddAccess = async (email, accessLevel) => {\r\n    try {\r\n      const response = await fetch(`${APIURL}/api/plans/${param}/access`, {\r\n        method: 'POST',\r\n        headers: {\r\n          ...getHeaders(),\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({ email, access_level: accessLevel })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const error = await response.json();\r\n        throw new Error(error.error || 'Failed to add access');\r\n      }\r\n\r\n      // Refresh plan data\r\n      await refreshPlanData();\r\n      toast.success('Access granted successfully');\r\n    } catch (error) {\r\n      console.error('Error adding access:', error);\r\n      toast.error(error.message || 'Failed to add access');\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const handleUpdateAccess = async (accessId, accessLevel, isHeadOwner = false) => {\r\n    try {\r\n      const response = await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          ...getHeaders(),\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({\r\n          access_level: accessLevel,\r\n          is_head_owner: isHeadOwner\r\n        })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const error = await response.json();\r\n        throw new Error(error.error || 'Failed to update access');\r\n      }\r\n\r\n      // Refresh plan data\r\n      await refreshPlanData();\r\n      toast.success('Access updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating access:', error);\r\n      toast.error(error.message || 'Failed to update access');\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const handleRemoveAccess = async (accessId) => {\r\n    try {\r\n      const response = await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`, {\r\n        method: 'DELETE',\r\n        headers: getHeaders()\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const error = await response.json();\r\n        throw new Error(error.error || 'Failed to remove access');\r\n      }\r\n\r\n      // Refresh plan data\r\n      await refreshPlanData();\r\n      toast.success('Access removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing access:', error);\r\n      toast.error(error.message || 'Failed to remove access');\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Handle milestone update\r\n  const handleUpdateMilestone = async (updatedMilestone) => {\r\n    try {\r\n      console.log('Updating milestone:', updatedMilestone);\r\n      await updateMilestone(updatedMilestone);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === updatedMilestone.id);\r\n\r\n      if (milestoneIndex !== -1) {\r\n        updatedPlan.milestones[milestoneIndex] = {\r\n          ...updatedPlan.milestones[milestoneIndex],\r\n          ...updatedMilestone\r\n        };\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Milestone updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating milestone:', error);\r\n      errorSnackbar('Failed to update milestone');\r\n    }\r\n  };\r\n\r\n  // Handle task update\r\n  const handleUpdateTask = async (updatedTask) => {\r\n    try {\r\n      console.log('Updating task:', updatedTask);\r\n      await updateTask(updatedTask);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m =>\r\n        m.tasks && m.tasks.some(t => t.id === updatedTask.id)\r\n      );\r\n\r\n      if (milestoneIndex !== -1) {\r\n        const taskIndex = updatedPlan.milestones[milestoneIndex].tasks.findIndex(t => t.id === updatedTask.id);\r\n\r\n        if (taskIndex !== -1) {\r\n          updatedPlan.milestones[milestoneIndex].tasks[taskIndex] = {\r\n            ...updatedPlan.milestones[milestoneIndex].tasks[taskIndex],\r\n            ...updatedTask\r\n          };\r\n          setPlan(updatedPlan);\r\n        }\r\n      }\r\n\r\n      successSnackbar('Task updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating task:', error);\r\n      errorSnackbar('Failed to update task');\r\n    }\r\n  };\r\n\r\n  // Handle subtask update\r\n  const handleUpdateSubtask = async (updatedSubtask) => {\r\n    try {\r\n      console.log('Updating subtask:', updatedSubtask);\r\n\r\n      // Call API to update subtask\r\n      await updateSubtask(updatedSubtask);\r\n\r\n      // Create a copy of current plan to update\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find the task containing the subtask\r\n      let taskFound = false;\r\n\r\n      // Update subtask in state\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (!task.subtasks) continue;\r\n\r\n          // Update subtask in task\r\n          const subtaskIndex = task.subtasks.findIndex(s => s.id === updatedSubtask.id);\r\n          if (subtaskIndex !== -1) {\r\n            // Update subtask\r\n            task.subtasks[subtaskIndex] = {\r\n              ...task.subtasks[subtaskIndex],\r\n              ...updatedSubtask\r\n            };\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      // Update state with new plan\r\n      setPlan(updatedPlan);\r\n\r\n      successSnackbar('Subtask updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating subtask:', error);\r\n      errorSnackbar('Failed to update subtask');\r\n    }\r\n  };\r\n\r\n  // Handle add task\r\n  const handleAddTask = async (newTask) => {\r\n    try {\r\n      console.log('Adding new task:', newTask);\r\n      const response = await addTask(newTask);\r\n      console.log('Add task response:', response);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find milestone to add new task\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === newTask.milestone);\r\n\r\n      if (milestoneIndex !== -1) {\r\n        // Add new task to milestone\r\n        if (!updatedPlan.milestones[milestoneIndex].tasks) {\r\n          updatedPlan.milestones[milestoneIndex].tasks = [];\r\n        }\r\n\r\n        // Add the new task with data from response\r\n        const taskToAdd = response.data || {\r\n          ...newTask,\r\n          id: Date.now(), // Temporary ID if response doesn't provide one\r\n          subtasks: []\r\n        };\r\n\r\n        updatedPlan.milestones[milestoneIndex].tasks.push(taskToAdd);\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Task added successfully');\r\n    } catch (error) {\r\n      console.error('Error adding task:', error);\r\n      errorSnackbar('Failed to add task');\r\n    }\r\n  };\r\n\r\n  // Handle add subtask\r\n  const handleAddSubtask = async (newSubtask) => {\r\n    try {\r\n      console.log('Adding new subtask:', newSubtask);\r\n      const response = await addSubtask(newSubtask);\r\n      console.log('Add subtask response:', response);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find task to add new subtask\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n\r\n          if (task.slug === newSubtask.task) {\r\n            // Add new subtask to task\r\n            if (!task.subtasks) {\r\n              task.subtasks = [];\r\n            }\r\n\r\n            // Add the new subtask with data from response\r\n            const subtaskToAdd = response.data || {\r\n              ...newSubtask,\r\n              id: Date.now() // Temporary ID if response doesn't provide one\r\n            };\r\n\r\n            task.subtasks.push(subtaskToAdd);\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      successSnackbar('Subtask added successfully');\r\n    } catch (error) {\r\n      console.error('Error adding subtask:', error);\r\n      errorSnackbar('Failed to add subtask');\r\n    }\r\n  };\r\n\r\n  // Handle delete task\r\n  const handleDeleteTask = async (taskToDelete) => {\r\n    try {\r\n      console.log('Deleting task:', taskToDelete);\r\n      await deleteTask(taskToDelete.slug);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find milestone containing the task to delete\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m =>\r\n        m.tasks && m.tasks.some(t => t.id === taskToDelete.id)\r\n      );\r\n\r\n      if (milestoneIndex !== -1) {\r\n        // Filter out the task to delete\r\n        updatedPlan.milestones[milestoneIndex].tasks = updatedPlan.milestones[milestoneIndex].tasks.filter(\r\n          t => t.id !== taskToDelete.id\r\n        );\r\n\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Task deleted successfully');\r\n    } catch (error) {\r\n      console.error('Error deleting task:', error);\r\n      errorSnackbar('Failed to delete task');\r\n    }\r\n  };\r\n\r\n  // Handle delete subtask\r\n  const handleDeleteSubtask = async (subtaskToDelete) => {\r\n    try {\r\n      console.log('Deleting subtask:', subtaskToDelete);\r\n      await deleteSubtask(subtaskToDelete.slug);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find task containing the subtask to delete\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (!task.subtasks) continue;\r\n\r\n          // Filter out the subtask to delete\r\n          const originalLength = task.subtasks.length;\r\n          task.subtasks = task.subtasks.filter(s => s.id !== subtaskToDelete.id);\r\n\r\n          if (task.subtasks.length < originalLength) {\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      toast.success('Subtask deleted successfully');\r\n    } catch (error) {\r\n      console.error('Error deleting subtask:', error);\r\n      toast.error('Failed to delete subtask');\r\n    }\r\n  };\r\n\r\n  // Handle assign members to task\r\n  const handleAssignMembers = async (taskToAssign, memberIds) => {\r\n    try {\r\n      console.log('Assigning members to task:', taskToAssign, memberIds);\r\n      await assignMembersToTask(taskToAssign.slug, memberIds);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find the task to update\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (task.id === taskToAssign.id) {\r\n            // Update assignees\r\n            // Find user objects for the selected member IDs\r\n            const assignedMembers = memberIds.map(memberId => {\r\n              // Check if it's the plan owner\r\n              if (planInfo.owner && planInfo.owner.id === memberId) {\r\n                return {\r\n                  id: planInfo.owner.id,\r\n                  first_name: planInfo.owner.first_name,\r\n                  last_name: planInfo.owner.last_name,\r\n                  email: planInfo.owner.email,\r\n                  avatar: planInfo.owner.avatar\r\n                };\r\n              }\r\n\r\n              // Check in invited users\r\n              const invitedUser = planInfo.invited_users?.find(\r\n                user => user.invited_user_info && user.invited_user_info.id === memberId\r\n              );\r\n\r\n              if (invitedUser) {\r\n                return {\r\n                  id: invitedUser.invited_user_info.id,\r\n                  first_name: invitedUser.invited_user_info.first_name,\r\n                  last_name: invitedUser.invited_user_info.last_name,\r\n                  email: invitedUser.email,\r\n                  avatar: invitedUser.invited_user_info.avatar\r\n                };\r\n              }\r\n\r\n              return null;\r\n            }).filter(member => member !== null);\r\n\r\n            task.assignees = assignedMembers;\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      toast.success('Members assigned successfully');\r\n    } catch (error) {\r\n      console.error('Error assigning members to task:', error);\r\n      toast.error('Failed to assign members to task');\r\n    }\r\n  };\r\n\r\n  if (error) {\r\n    return (\r\n      <Container className={styles.container}>\r\n        <Alert severity=\"error\" sx={{ mt: 4 }}>\r\n          An error occurred while loading plan information. Please try again later.\r\n        </Alert>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container\r\n      maxWidth=\"lg\"\r\n      className={styles.container}\r\n      sx={{\r\n        padding: '20px',\r\n        minHeight: 'calc(100vh - 65px)',\r\n        fontFamily: '\"Recursive Variable\", sans-serif'\r\n      }}\r\n    >\r\n      {loading ? (\r\n        <Box className={styles.loadingContainer}>\r\n          <CircularProgress size={60} sx={{ color: mainYellowColor }} />\r\n        </Box>\r\n      ) : (\r\n        <>\r\n          {/* Header Section */}\r\n          <Header\r\n            planInfo={planInfo}\r\n            viewMode={viewMode}\r\n            onViewModeChange={handleViewModeChange}\r\n            onOpenInviteDialog={() => openDialog('invite')}\r\n            onOpenDeleteDialog={() => openDialog('delete')}\r\n            onOpenOptOutDialog={() => openDialog('optOut')}\r\n          />\r\n\r\n          {/* Tabs Navigation */}\r\n          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 0.5, mt: -1 }}>\r\n            <Tabs\r\n              value={activeTab}\r\n              onChange={handleTabChange}\r\n              variant=\"scrollable\"\r\n              scrollButtons=\"auto\"\r\n              sx={{\r\n                minHeight: '36px',\r\n                '& .MuiTab-root': {\r\n                  textTransform: 'none',\r\n                  fontWeight: 600,\r\n                  fontSize: '0.9rem',\r\n                  minWidth: 'auto',\r\n                  minHeight: '36px',\r\n                  px: 2,\r\n                  py: 0.5,\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                },\r\n                '& .Mui-selected': {\r\n                  color: `${mainYellowColor} !important`,\r\n                },\r\n                '& .MuiTabs-indicator': {\r\n                  backgroundColor: mainYellowColor,\r\n                  height: '2px'\r\n                }\r\n              }}\r\n            >\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:dashboard\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Overview\"\r\n                value=\"overview\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:flag\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Milestones\"\r\n                value=\"milestones\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:task\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Tasks\"\r\n                value=\"tasks\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:people\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Members\"\r\n                value=\"members\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              {planInfo?.user_access_level?.access_level === 'owner' && (\r\n                <Tab\r\n                  icon={<Iconify icon=\"material-symbols:security\" width={16} height={16} />}\r\n                  iconPosition=\"start\"\r\n                  label=\"Access\"\r\n                  value=\"access\"\r\n                  sx={{ gap: '4px' }}\r\n                />\r\n              )}\r\n            </Tabs>\r\n          </Box>\r\n\r\n          {/* Tab Content */}\r\n          <Box className={styles.tabContent}>\r\n            {activeTab === 'overview' && (\r\n              <Box className={styles.overviewTab} sx={{ gap: 0.5 }}>\r\n                <Description planInfo={planInfo} />\r\n                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 1, mb: 0.5 }}>\r\n                  <Statistics stats={stats} />\r\n                  <Progress stats={stats} />\r\n                </Box>\r\n                <Members\r\n                  invitedUsers={invitedUsers}\r\n                  planOwner={planInfo?.user}\r\n                  onInvite={() => openDialog('invite')}\r\n                />\r\n                <MilestoneOverview\r\n                  milestones={planInfo?.milestones}\r\n                  calculateMilestoneProgress={calculateMilestoneProgress}\r\n                  getMilestoneStatus={getMilestoneStatus}\r\n                  calculateTaskProgress={calculateTaskProgress}\r\n                  getTaskStatus={getTaskStatus}\r\n                  calculateSubtaskProgress={calculateSubtaskProgress}\r\n                  getSubtaskStatus={getSubtaskStatus}\r\n                />\r\n              </Box>\r\n            )}\r\n\r\n            {activeTab === 'milestones' && (\r\n              <MilestoneList\r\n                milestones={planInfo?.milestones}\r\n                viewMode={viewMode}\r\n                compact={false}\r\n                showSubtasks={true}\r\n                calculateMilestoneProgress={calculateMilestoneProgress}\r\n                getMilestoneStatus={getMilestoneStatus}\r\n                calculateTaskProgress={calculateTaskProgress}\r\n                getTaskStatus={getTaskStatus}\r\n                calculateSubtaskProgress={calculateSubtaskProgress}\r\n                getSubtaskStatus={getSubtaskStatus}\r\n                onUpdateMilestone={handleUpdateMilestone}\r\n                onUpdateTask={handleUpdateTask}\r\n                onUpdateSubtask={handleUpdateSubtask}\r\n                onAddTask={handleAddTask}\r\n                onAddSubtask={handleAddSubtask}\r\n                onDeleteTask={handleDeleteTask}\r\n                onDeleteSubtask={handleDeleteSubtask}\r\n                onAssignMembers={handleAssignMembers}\r\n                invitedUsers={planInfo?.invited_users || []}\r\n                planOwner={planInfo?.owner}\r\n              />\r\n            )}\r\n\r\n            {activeTab === 'tasks' && (\r\n              <TaskList\r\n                milestones={planInfo?.milestones}\r\n                onUpdateTask={handleUpdateTask}\r\n                onUpdateSubtask={handleUpdateSubtask}\r\n                onAddSubtask={handleAddSubtask}\r\n                onDeleteTask={handleDeleteTask}\r\n                onDeleteSubtask={handleDeleteSubtask}\r\n              />\r\n            )}\r\n\r\n            {activeTab === 'members' && (\r\n              <Members\r\n                invitedUsers={invitedUsers}\r\n                planOwner={planInfo?.user}\r\n                onInvite={() => openDialog('invite')}\r\n                expanded={true}\r\n              />\r\n            )}\r\n\r\n            {activeTab === 'access' && planInfo?.user_access_level?.access_level === 'owner' && (\r\n              <AccessManagement\r\n                planInfo={planInfo}\r\n                userAccessLevel={planInfo?.user_access_level}\r\n                onAddAccess={handleAddAccess}\r\n                onUpdateAccess={handleUpdateAccess}\r\n                onRemoveAccess={handleRemoveAccess}\r\n              />\r\n            )}\r\n          </Box>\r\n        </>\r\n      )}\r\n\r\n      {/* Dialogs */}\r\n      <InviteDialog\r\n        open={dialogState.invite}\r\n        onClose={() => closeDialog('invite')}\r\n        onInvite={handleInviteUser}\r\n        planInfo={planInfo}\r\n      />\r\n\r\n      <DeleteDialog\r\n        open={dialogState.delete}\r\n        onClose={() => closeDialog('delete')}\r\n        onDelete={handleDeletePlan}\r\n      />\r\n\r\n      <OptOutDialog\r\n        open={dialogState.optOut}\r\n        onClose={() => closeDialog('optOut')}\r\n        onOptOut={handleOptOutPlan}\r\n      />\r\n\r\n      <ConfirmDialog\r\n        open={dialogState.deleteTask}\r\n        onClose={() => setDialogState(prev => ({ ...prev, deleteTask: false }))}\r\n        onConfirm={() => handleDeleteTask(selectedTaskToDelete)}\r\n        title=\"Delete Task\"\r\n        description={`Are you sure you want to delete the task \"${selectedTaskToDelete?.name}\"? This action cannot be undone.`}\r\n      />\r\n\r\n      <ConfirmDialog\r\n        open={dialogState.deleteSubtask}\r\n        onClose={() => setDialogState(prev => ({ ...prev, deleteSubtask: false }))}\r\n        onConfirm={() => handleDeleteSubtask(selectedSubtaskToDelete)}\r\n        title=\"Delete Subtask\"\r\n        description={`Are you sure you want to delete the subtask \"${selectedSubtaskToDelete?.name}\"? This action cannot be undone.`}\r\n      />\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default PlanDetail;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,KAAQ,kBAAkB,CAC5C,OAASC,SAAS,CAAEC,GAAG,CAAEC,IAAI,CAAEC,GAAG,CAAEC,gBAAgB,CAAEC,KAAK,KAAQ,eAAe,CAClF,MAAO,CAAAC,OAAO,KAAM,0BAA0B,CAC9C,OAASC,eAAe,CAAEC,MAAM,KAAQ,mBAAmB,CAC3D,OAASC,eAAe,CAAEC,aAAa,KAAQ,2BAA2B,CAC1E,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,UAAU,KAAQ,mBAAmB,CAE9C;AACA,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,QAAQ,KAAM,uBAAuB,CAC5C,MAAO,CAAAC,OAAO,KAAM,sBAAsB,CAC1C,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,QAAQ,KAAM,uBAAuB,CAC5C,MAAO,CAAAC,iBAAiB,KAAM,gCAAgC,CAC9D,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAE5D;AACA,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAE7C;AACA,MAAO,CAAAC,YAAY,KAAM,wBAAwB,CACjD,MAAO,CAAAC,YAAY,KAAM,wBAAwB,CACjD,MAAO,CAAAC,YAAY,KAAM,wBAAwB,CACjD,MAAO,CAAAC,aAAa,KAAM,yBAAyB,CAEnD;AACA,OACEC,eAAe,CACfC,UAAU,CACVC,aAAa,CACbC,OAAO,CACPC,UAAU,CACVC,UAAU,CACVC,aAAa,CACbC,mBAAmB,KACd,aAAa,CAEpB;AACA,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1C,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CACvB,KAAM,CAAEC,KAAM,CAAC,CAAG/C,SAAS,CAAC,CAAC,CAC7B,KAAM,CAACgD,SAAS,CAAEC,YAAY,CAAC,CAAGnD,QAAQ,CAAC,IAAM,CAC/C;AACA,MAAO,CAAAoD,YAAY,CAACC,OAAO,CAAC,QAAQJ,KAAK,YAAY,CAAC,EAAI,UAAU,CACtE,CAAC,CAAC,CACF,KAAM,CAACK,IAAI,CAAEC,OAAO,CAAC,CAAGvD,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACwD,WAAW,CAAEC,cAAc,CAAC,CAAGzD,QAAQ,CAAC,CAC7C0D,MAAM,CAAE,KAAK,CACbC,MAAM,CAAE,KAAK,CACbC,MAAM,CAAE,KAAK,CACbxB,UAAU,CAAE,KAAK,CACjBC,aAAa,CAAE,KACjB,CAAC,CAAC,CACF,KAAM,CAACwB,oBAAoB,CAAG,CAAG7D,QAAQ,CAAC,IAAI,CAAC,CAC/C,KAAM,CAAC8D,uBAAuB,CAAG,CAAG9D,QAAQ,CAAC,IAAI,CAAC,CAElD;AACA,KAAM,CACJ+D,QAAQ,CACRC,OAAO,CACPC,KAAK,CACLC,YAAY,CACZC,gBAAgB,CAChBC,gBAAgB,CAChBC,gBAAgB,CAChBC,kBAAkB,CAClBC,wBAAwB,CACxBC,gBAAgB,CAChBC,qBAAqB,CACrBC,aAAa,CACbC,0BAA0B,CAC1BC,kBAAkB,CAClBC,iBACF,CAAC,CAAGpD,WAAW,CAACwB,KAAK,CAAC,CAEtB,KAAM,CACJ6B,QAAQ,CACRC,oBACF,CAAC,CAAGrD,WAAW,CAAC,CAAC,CAEjB;AACAzB,SAAS,CAAC,IAAM,CACd,GAAI8D,QAAQ,CAAE,CACZR,OAAO,CAACQ,QAAQ,CAAC,CACnB,CACF,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEd;AACA9D,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACXmD,YAAY,CAAC4B,UAAU,CAAC,QAAQ/B,KAAK,YAAY,CAAC,CACpD,CAAC,CACH,CAAC,CAAE,CAACA,KAAK,CAAC,CAAC,CAEX;AACA,KAAM,CAAAgC,KAAK,CAAGX,kBAAkB,CAAGA,kBAAkB,CAACP,QAAQ,CAAC,CAAG,IAAI,CAEtE;AACA,KAAM,CAAAmB,UAAU,CAAIC,UAAU,EAAK,CACjC1B,cAAc,CAAC2B,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,UAAU,EAAG,IAAK,CAAC,CAAC,CAAC,CAC3D,CAAC,CAED,KAAM,CAAAE,WAAW,CAAIF,UAAU,EAAK,CAClC1B,cAAc,CAAC2B,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,UAAU,EAAG,KAAM,CAAC,CAAC,CAAC,CAC5D,CAAC,CAED;AACA,KAAM,CAAAG,eAAe,CAAGA,CAACC,CAAC,CAAEC,QAAQ,GAAK,CACvCrC,YAAY,CAACqC,QAAQ,CAAC,CACtB;AACApC,YAAY,CAACqC,OAAO,CAAC,QAAQxC,KAAK,YAAY,CAAEuC,QAAQ,CAAC,CAC3D,CAAC,CAED;AACA,KAAM,CAAAE,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACFC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAE;AAC5B,CAAE,MAAO5B,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CACF,CAAC,CAED;AACA,KAAM,CAAA8B,eAAe,CAAG,KAAAA,CAAOC,KAAK,CAAEC,WAAW,GAAK,CACpD,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGxF,MAAM,cAAcsC,KAAK,SAAS,CAAE,CAClEmD,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,GAAGtF,UAAU,CAAC,CAAC,CACf,cAAc,CAAE,kBAClB,CAAC,CACDuF,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAER,KAAK,CAAES,YAAY,CAAER,WAAY,CAAC,CAC3D,CAAC,CAAC,CAEF,GAAI,CAACC,QAAQ,CAACQ,EAAE,CAAE,CAChB,KAAM,CAAAzC,KAAK,CAAG,KAAM,CAAAiC,QAAQ,CAACS,IAAI,CAAC,CAAC,CACnC,KAAM,IAAI,CAAAC,KAAK,CAAC3C,KAAK,CAACA,KAAK,EAAI,sBAAsB,CAAC,CACxD,CAEA;AACA,KAAM,CAAAyB,eAAe,CAAC,CAAC,CACvB5E,KAAK,CAAC+F,OAAO,CAAC,6BAA6B,CAAC,CAC9C,CAAE,MAAO5C,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CnD,KAAK,CAACmD,KAAK,CAACA,KAAK,CAAC6C,OAAO,EAAI,sBAAsB,CAAC,CACpD,KAAM,CAAA7C,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAA8C,kBAAkB,CAAG,cAAAA,CAAOC,QAAQ,CAAEf,WAAW,CAA0B,IAAxB,CAAAgB,WAAW,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC1E,GAAI,CACF,KAAM,CAAAhB,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGxF,MAAM,cAAcsC,KAAK,WAAW+D,QAAQ,EAAE,CAAE,CAC9EZ,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,GAAGtF,UAAU,CAAC,CAAC,CACf,cAAc,CAAE,kBAClB,CAAC,CACDuF,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBC,YAAY,CAAER,WAAW,CACzBoB,aAAa,CAAEJ,WACjB,CAAC,CACH,CAAC,CAAC,CAEF,GAAI,CAACf,QAAQ,CAACQ,EAAE,CAAE,CAChB,KAAM,CAAAzC,KAAK,CAAG,KAAM,CAAAiC,QAAQ,CAACS,IAAI,CAAC,CAAC,CACnC,KAAM,IAAI,CAAAC,KAAK,CAAC3C,KAAK,CAACA,KAAK,EAAI,yBAAyB,CAAC,CAC3D,CAEA;AACA,KAAM,CAAAyB,eAAe,CAAC,CAAC,CACvB5E,KAAK,CAAC+F,OAAO,CAAC,6BAA6B,CAAC,CAC9C,CAAE,MAAO5C,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CnD,KAAK,CAACmD,KAAK,CAACA,KAAK,CAAC6C,OAAO,EAAI,yBAAyB,CAAC,CACvD,KAAM,CAAA7C,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAAqD,kBAAkB,CAAG,KAAO,CAAAN,QAAQ,EAAK,CAC7C,GAAI,CACF,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGxF,MAAM,cAAcsC,KAAK,WAAW+D,QAAQ,EAAE,CAAE,CAC9EZ,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAEtF,UAAU,CAAC,CACtB,CAAC,CAAC,CAEF,GAAI,CAACmF,QAAQ,CAACQ,EAAE,CAAE,CAChB,KAAM,CAAAzC,KAAK,CAAG,KAAM,CAAAiC,QAAQ,CAACS,IAAI,CAAC,CAAC,CACnC,KAAM,IAAI,CAAAC,KAAK,CAAC3C,KAAK,CAACA,KAAK,EAAI,yBAAyB,CAAC,CAC3D,CAEA;AACA,KAAM,CAAAyB,eAAe,CAAC,CAAC,CACvB5E,KAAK,CAAC+F,OAAO,CAAC,6BAA6B,CAAC,CAC9C,CAAE,MAAO5C,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CnD,KAAK,CAACmD,KAAK,CAACA,KAAK,CAAC6C,OAAO,EAAI,yBAAyB,CAAC,CACvD,KAAM,CAAA7C,KAAK,CACb,CACF,CAAC,CAED;AACA,KAAM,CAAAsD,qBAAqB,CAAG,KAAO,CAAAC,gBAAgB,EAAK,CACxD,GAAI,CACF1B,OAAO,CAAC2B,GAAG,CAAC,qBAAqB,CAAED,gBAAgB,CAAC,CACpD,KAAM,CAAAzF,eAAe,CAACyF,gBAAgB,CAAC,CAEvC;AACA,KAAM,CAAAE,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAC/B,KAAM,CAAAqE,cAAc,CAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,EAAE,GAAKP,gBAAgB,CAACO,EAAE,CAAC,CAE1F,GAAIJ,cAAc,GAAK,CAAC,CAAC,CAAE,CACzBD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAAG,CACvC,GAAGD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CACzC,GAAGH,gBACL,CAAC,CACDjE,OAAO,CAACmE,WAAW,CAAC,CACtB,CAEA9G,eAAe,CAAC,gCAAgC,CAAC,CACnD,CAAE,MAAOqD,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDpD,aAAa,CAAC,4BAA4B,CAAC,CAC7C,CACF,CAAC,CAED;AACA,KAAM,CAAAmH,gBAAgB,CAAG,KAAO,CAAAC,WAAW,EAAK,CAC9C,GAAI,CACFnC,OAAO,CAAC2B,GAAG,CAAC,gBAAgB,CAAEQ,WAAW,CAAC,CAC1C,KAAM,CAAAjG,UAAU,CAACiG,WAAW,CAAC,CAE7B;AACA,KAAM,CAAAP,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAC/B,KAAM,CAAAqE,cAAc,CAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,EACvDA,CAAC,CAACI,KAAK,EAAIJ,CAAC,CAACI,KAAK,CAACC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACL,EAAE,GAAKE,WAAW,CAACF,EAAE,CACtD,CAAC,CAED,GAAIJ,cAAc,GAAK,CAAC,CAAC,CAAE,CACzB,KAAM,CAAAU,SAAS,CAAGX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACL,SAAS,CAACO,CAAC,EAAIA,CAAC,CAACL,EAAE,GAAKE,WAAW,CAACF,EAAE,CAAC,CAEtG,GAAIM,SAAS,GAAK,CAAC,CAAC,CAAE,CACpBX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACG,SAAS,CAAC,CAAG,CACxD,GAAGX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACG,SAAS,CAAC,CAC1D,GAAGJ,WACL,CAAC,CACD1E,OAAO,CAACmE,WAAW,CAAC,CACtB,CACF,CAEA9G,eAAe,CAAC,2BAA2B,CAAC,CAC9C,CAAE,MAAOqD,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CpD,aAAa,CAAC,uBAAuB,CAAC,CACxC,CACF,CAAC,CAED;AACA,KAAM,CAAAyH,mBAAmB,CAAG,KAAO,CAAAC,cAAc,EAAK,CACpD,GAAI,CACFzC,OAAO,CAAC2B,GAAG,CAAC,mBAAmB,CAAEc,cAAc,CAAC,CAEhD;AACA,KAAM,CAAAtG,aAAa,CAACsG,cAAc,CAAC,CAEnC;AACA,KAAM,CAAAb,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,GAAI,CAAAkF,SAAS,CAAG,KAAK,CAErB;AACA,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGf,WAAW,CAACE,UAAU,CAACT,MAAM,CAAEsB,CAAC,EAAE,CAAE,CACtD,KAAM,CAAAC,SAAS,CAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC,CAC3C,GAAI,CAACC,SAAS,CAACR,KAAK,CAAE,SAEtB,IAAK,GAAI,CAAAS,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,SAAS,CAACR,KAAK,CAACf,MAAM,CAAEwB,CAAC,EAAE,CAAE,CAC/C,KAAM,CAAAC,IAAI,CAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC,CAC/B,GAAI,CAACC,IAAI,CAACC,QAAQ,CAAE,SAEpB;AACA,KAAM,CAAAC,YAAY,CAAGF,IAAI,CAACC,QAAQ,CAAChB,SAAS,CAACkB,CAAC,EAAIA,CAAC,CAAChB,EAAE,GAAKQ,cAAc,CAACR,EAAE,CAAC,CAC7E,GAAIe,YAAY,GAAK,CAAC,CAAC,CAAE,CACvB;AACAF,IAAI,CAACC,QAAQ,CAACC,YAAY,CAAC,CAAG,CAC5B,GAAGF,IAAI,CAACC,QAAQ,CAACC,YAAY,CAAC,CAC9B,GAAGP,cACL,CAAC,CACDC,SAAS,CAAG,IAAI,CAChB,MACF,CACF,CAEA,GAAIA,SAAS,CAAE,MACjB,CAEA;AACAjF,OAAO,CAACmE,WAAW,CAAC,CAEpB9G,eAAe,CAAC,8BAA8B,CAAC,CACjD,CAAE,MAAOqD,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CpD,aAAa,CAAC,0BAA0B,CAAC,CAC3C,CACF,CAAC,CAED;AACA,KAAM,CAAAmI,aAAa,CAAG,KAAO,CAAAC,OAAO,EAAK,CACvC,GAAI,CACFnD,OAAO,CAAC2B,GAAG,CAAC,kBAAkB,CAAEwB,OAAO,CAAC,CACxC,KAAM,CAAA/C,QAAQ,CAAG,KAAM,CAAAhE,OAAO,CAAC+G,OAAO,CAAC,CACvCnD,OAAO,CAAC2B,GAAG,CAAC,oBAAoB,CAAEvB,QAAQ,CAAC,CAE3C;AACA,KAAM,CAAAwB,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,KAAM,CAAAqE,cAAc,CAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,EAAE,GAAKkB,OAAO,CAACP,SAAS,CAAC,CAExF,GAAIf,cAAc,GAAK,CAAC,CAAC,CAAE,CACzB;AACA,GAAI,CAACD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAAE,CACjDR,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAAG,EAAE,CACnD,CAEA;AACA,KAAM,CAAAgB,SAAS,CAAGhD,QAAQ,CAACiD,IAAI,EAAI,CACjC,GAAGF,OAAO,CACVlB,EAAE,CAAEqB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE;AAChBR,QAAQ,CAAE,EACZ,CAAC,CAEDnB,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACoB,IAAI,CAACJ,SAAS,CAAC,CAC5D3F,OAAO,CAACmE,WAAW,CAAC,CACtB,CAEA9G,eAAe,CAAC,yBAAyB,CAAC,CAC5C,CAAE,MAAOqD,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1CpD,aAAa,CAAC,oBAAoB,CAAC,CACrC,CACF,CAAC,CAED;AACA,KAAM,CAAA0I,gBAAgB,CAAG,KAAO,CAAAC,UAAU,EAAK,CAC7C,GAAI,CACF1D,OAAO,CAAC2B,GAAG,CAAC,qBAAqB,CAAE+B,UAAU,CAAC,CAC9C,KAAM,CAAAtD,QAAQ,CAAG,KAAM,CAAA/D,UAAU,CAACqH,UAAU,CAAC,CAC7C1D,OAAO,CAAC2B,GAAG,CAAC,uBAAuB,CAAEvB,QAAQ,CAAC,CAE9C;AACA,KAAM,CAAAwB,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,GAAI,CAAAkF,SAAS,CAAG,KAAK,CAErB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGf,WAAW,CAACE,UAAU,CAACT,MAAM,CAAEsB,CAAC,EAAE,CAAE,CACtD,KAAM,CAAAC,SAAS,CAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC,CAC3C,GAAI,CAACC,SAAS,CAACR,KAAK,CAAE,SAEtB,IAAK,GAAI,CAAAS,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,SAAS,CAACR,KAAK,CAACf,MAAM,CAAEwB,CAAC,EAAE,CAAE,CAC/C,KAAM,CAAAC,IAAI,CAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC,CAE/B,GAAIC,IAAI,CAACa,IAAI,GAAKD,UAAU,CAACZ,IAAI,CAAE,CACjC;AACA,GAAI,CAACA,IAAI,CAACC,QAAQ,CAAE,CAClBD,IAAI,CAACC,QAAQ,CAAG,EAAE,CACpB,CAEA;AACA,KAAM,CAAAa,YAAY,CAAGxD,QAAQ,CAACiD,IAAI,EAAI,CACpC,GAAGK,UAAU,CACbzB,EAAE,CAAEqB,IAAI,CAACC,GAAG,CAAC,CAAE;AACjB,CAAC,CAEDT,IAAI,CAACC,QAAQ,CAACS,IAAI,CAACI,YAAY,CAAC,CAChClB,SAAS,CAAG,IAAI,CAChB,MACF,CACF,CAEA,GAAIA,SAAS,CAAE,MACjB,CAEAjF,OAAO,CAACmE,WAAW,CAAC,CACpB9G,eAAe,CAAC,4BAA4B,CAAC,CAC/C,CAAE,MAAOqD,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CpD,aAAa,CAAC,uBAAuB,CAAC,CACxC,CACF,CAAC,CAED;AACA,KAAM,CAAA8I,gBAAgB,CAAG,KAAO,CAAAC,YAAY,EAAK,CAC/C,GAAI,CACF9D,OAAO,CAAC2B,GAAG,CAAC,gBAAgB,CAAEmC,YAAY,CAAC,CAC3C,KAAM,CAAAxH,UAAU,CAACwH,YAAY,CAACH,IAAI,CAAC,CAEnC;AACA,KAAM,CAAA/B,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,KAAM,CAAAqE,cAAc,CAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,EACvDA,CAAC,CAACI,KAAK,EAAIJ,CAAC,CAACI,KAAK,CAACC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACL,EAAE,GAAK6B,YAAY,CAAC7B,EAAE,CACvD,CAAC,CAED,GAAIJ,cAAc,GAAK,CAAC,CAAC,CAAE,CACzB;AACAD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAAGR,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAAC2B,MAAM,CAChGzB,CAAC,EAAIA,CAAC,CAACL,EAAE,GAAK6B,YAAY,CAAC7B,EAC7B,CAAC,CAEDxE,OAAO,CAACmE,WAAW,CAAC,CACtB,CAEA9G,eAAe,CAAC,2BAA2B,CAAC,CAC9C,CAAE,MAAOqD,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CpD,aAAa,CAAC,uBAAuB,CAAC,CACxC,CACF,CAAC,CAED;AACA,KAAM,CAAAiJ,mBAAmB,CAAG,KAAO,CAAAC,eAAe,EAAK,CACrD,GAAI,CACFjE,OAAO,CAAC2B,GAAG,CAAC,mBAAmB,CAAEsC,eAAe,CAAC,CACjD,KAAM,CAAA1H,aAAa,CAAC0H,eAAe,CAACN,IAAI,CAAC,CAEzC;AACA,KAAM,CAAA/B,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,GAAI,CAAAkF,SAAS,CAAG,KAAK,CAErB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGf,WAAW,CAACE,UAAU,CAACT,MAAM,CAAEsB,CAAC,EAAE,CAAE,CACtD,KAAM,CAAAC,SAAS,CAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC,CAC3C,GAAI,CAACC,SAAS,CAACR,KAAK,CAAE,SAEtB,IAAK,GAAI,CAAAS,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,SAAS,CAACR,KAAK,CAACf,MAAM,CAAEwB,CAAC,EAAE,CAAE,CAC/C,KAAM,CAAAC,IAAI,CAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC,CAC/B,GAAI,CAACC,IAAI,CAACC,QAAQ,CAAE,SAEpB;AACA,KAAM,CAAAmB,cAAc,CAAGpB,IAAI,CAACC,QAAQ,CAAC1B,MAAM,CAC3CyB,IAAI,CAACC,QAAQ,CAAGD,IAAI,CAACC,QAAQ,CAACgB,MAAM,CAACd,CAAC,EAAIA,CAAC,CAAChB,EAAE,GAAKgC,eAAe,CAAChC,EAAE,CAAC,CAEtE,GAAIa,IAAI,CAACC,QAAQ,CAAC1B,MAAM,CAAG6C,cAAc,CAAE,CACzCxB,SAAS,CAAG,IAAI,CAChB,MACF,CACF,CAEA,GAAIA,SAAS,CAAE,MACjB,CAEAjF,OAAO,CAACmE,WAAW,CAAC,CACpB5G,KAAK,CAAC+F,OAAO,CAAC,8BAA8B,CAAC,CAC/C,CAAE,MAAO5C,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CnD,KAAK,CAACmD,KAAK,CAAC,0BAA0B,CAAC,CACzC,CACF,CAAC,CAED;AACA,KAAM,CAAAgG,mBAAmB,CAAG,KAAAA,CAAOC,YAAY,CAAEC,SAAS,GAAK,CAC7D,GAAI,CACFrE,OAAO,CAAC2B,GAAG,CAAC,4BAA4B,CAAEyC,YAAY,CAAEC,SAAS,CAAC,CAClE,KAAM,CAAA7H,mBAAmB,CAAC4H,YAAY,CAACT,IAAI,CAAEU,SAAS,CAAC,CAEvD;AACA,KAAM,CAAAzC,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,GAAI,CAAAkF,SAAS,CAAG,KAAK,CAErB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGf,WAAW,CAACE,UAAU,CAACT,MAAM,CAAEsB,CAAC,EAAE,CAAE,CACtD,KAAM,CAAAC,SAAS,CAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC,CAC3C,GAAI,CAACC,SAAS,CAACR,KAAK,CAAE,SAEtB,IAAK,GAAI,CAAAS,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,SAAS,CAACR,KAAK,CAACf,MAAM,CAAEwB,CAAC,EAAE,CAAE,CAC/C,KAAM,CAAAC,IAAI,CAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC,CAC/B,GAAIC,IAAI,CAACb,EAAE,GAAKmC,YAAY,CAACnC,EAAE,CAAE,CAC/B;AACA;AACA,KAAM,CAAAqC,eAAe,CAAGD,SAAS,CAACE,GAAG,CAACC,QAAQ,EAAI,KAAAC,qBAAA,CAChD;AACA,GAAIxG,QAAQ,CAACyG,KAAK,EAAIzG,QAAQ,CAACyG,KAAK,CAACzC,EAAE,GAAKuC,QAAQ,CAAE,CACpD,MAAO,CACLvC,EAAE,CAAEhE,QAAQ,CAACyG,KAAK,CAACzC,EAAE,CACrB0C,UAAU,CAAE1G,QAAQ,CAACyG,KAAK,CAACC,UAAU,CACrCC,SAAS,CAAE3G,QAAQ,CAACyG,KAAK,CAACE,SAAS,CACnC1E,KAAK,CAAEjC,QAAQ,CAACyG,KAAK,CAACxE,KAAK,CAC3B2E,MAAM,CAAE5G,QAAQ,CAACyG,KAAK,CAACG,MACzB,CAAC,CACH,CAEA;AACA,KAAM,CAAAC,WAAW,EAAAL,qBAAA,CAAGxG,QAAQ,CAAC8G,aAAa,UAAAN,qBAAA,iBAAtBA,qBAAA,CAAwBO,IAAI,CAC9CC,IAAI,EAAIA,IAAI,CAACC,iBAAiB,EAAID,IAAI,CAACC,iBAAiB,CAACjD,EAAE,GAAKuC,QAClE,CAAC,CAED,GAAIM,WAAW,CAAE,CACf,MAAO,CACL7C,EAAE,CAAE6C,WAAW,CAACI,iBAAiB,CAACjD,EAAE,CACpC0C,UAAU,CAAEG,WAAW,CAACI,iBAAiB,CAACP,UAAU,CACpDC,SAAS,CAAEE,WAAW,CAACI,iBAAiB,CAACN,SAAS,CAClD1E,KAAK,CAAE4E,WAAW,CAAC5E,KAAK,CACxB2E,MAAM,CAAEC,WAAW,CAACI,iBAAiB,CAACL,MACxC,CAAC,CACH,CAEA,MAAO,KAAI,CACb,CAAC,CAAC,CAACd,MAAM,CAACoB,MAAM,EAAIA,MAAM,GAAK,IAAI,CAAC,CAEpCrC,IAAI,CAACsC,SAAS,CAAGd,eAAe,CAChC5B,SAAS,CAAG,IAAI,CAChB,MACF,CACF,CAEA,GAAIA,SAAS,CAAE,MACjB,CAEAjF,OAAO,CAACmE,WAAW,CAAC,CACpB5G,KAAK,CAAC+F,OAAO,CAAC,+BAA+B,CAAC,CAChD,CAAE,MAAO5C,KAAK,CAAE,CACd6B,OAAO,CAAC7B,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxDnD,KAAK,CAACmD,KAAK,CAAC,kCAAkC,CAAC,CACjD,CACF,CAAC,CAED,GAAIA,KAAK,CAAE,CACT,mBACExB,IAAA,CAACtC,SAAS,EAACgL,SAAS,CAAE5I,MAAM,CAAC6I,SAAU,CAAAC,QAAA,cACrC5I,IAAA,CAACjC,KAAK,EAAC8K,QAAQ,CAAC,OAAO,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CAAC,2EAEvC,CAAO,CAAC,CACC,CAAC,CAEhB,CAEA,mBACE1I,KAAA,CAACxC,SAAS,EACRsL,QAAQ,CAAC,IAAI,CACbN,SAAS,CAAE5I,MAAM,CAAC6I,SAAU,CAC5BG,EAAE,CAAE,CACFG,OAAO,CAAE,MAAM,CACfC,SAAS,CAAE,oBAAoB,CAC/BC,UAAU,CAAE,kCACd,CAAE,CAAAP,QAAA,EAEDrH,OAAO,cACNvB,IAAA,CAACrC,GAAG,EAAC+K,SAAS,CAAE5I,MAAM,CAACsJ,gBAAiB,CAAAR,QAAA,cACtC5I,IAAA,CAAClC,gBAAgB,EAACuL,IAAI,CAAE,EAAG,CAACP,EAAE,CAAE,CAAEQ,KAAK,CAAErL,eAAgB,CAAE,CAAE,CAAC,CAC3D,CAAC,cAENiC,KAAA,CAAAE,SAAA,EAAAwI,QAAA,eAEE5I,IAAA,CAACzB,MAAM,EACL+C,QAAQ,CAAEA,QAAS,CACnBe,QAAQ,CAAEA,QAAS,CACnBkH,gBAAgB,CAAEjH,oBAAqB,CACvCkH,kBAAkB,CAAEA,CAAA,GAAM/G,UAAU,CAAC,QAAQ,CAAE,CAC/CgH,kBAAkB,CAAEA,CAAA,GAAMhH,UAAU,CAAC,QAAQ,CAAE,CAC/CiH,kBAAkB,CAAEA,CAAA,GAAMjH,UAAU,CAAC,QAAQ,CAAE,CAChD,CAAC,cAGFzC,IAAA,CAACrC,GAAG,EAACmL,EAAE,CAAE,CAAEa,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAS,CAAEC,EAAE,CAAE,GAAG,CAAEd,EAAE,CAAE,CAAC,CAAE,CAAE,CAAAH,QAAA,cACpE1I,KAAA,CAACtC,IAAI,EACHkM,KAAK,CAAErJ,SAAU,CACjBsJ,QAAQ,CAAElH,eAAgB,CAC1BmH,OAAO,CAAC,YAAY,CACpBC,aAAa,CAAC,MAAM,CACpBnB,EAAE,CAAE,CACFI,SAAS,CAAE,MAAM,CACjB,gBAAgB,CAAE,CAChBgB,aAAa,CAAE,MAAM,CACrBC,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,QAAQ,CAClBC,QAAQ,CAAE,MAAM,CAChBnB,SAAS,CAAE,MAAM,CACjBoB,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPpB,UAAU,CAAE,kCACd,CAAC,CACD,iBAAiB,CAAE,CACjBG,KAAK,CAAE,GAAGrL,eAAe,aAC3B,CAAC,CACD,sBAAsB,CAAE,CACtBuM,eAAe,CAAEvM,eAAe,CAChCwM,MAAM,CAAE,KACV,CACF,CAAE,CAAA7B,QAAA,eAEF5I,IAAA,CAACnC,GAAG,EACF6M,IAAI,cAAE1K,IAAA,CAAChC,OAAO,EAAC0M,IAAI,CAAC,4BAA4B,CAACC,KAAK,CAAE,EAAG,CAACF,MAAM,CAAE,EAAG,CAAE,CAAE,CAC3EG,YAAY,CAAC,OAAO,CACpBC,KAAK,CAAC,UAAU,CAChBf,KAAK,CAAC,UAAU,CAChBhB,EAAE,CAAE,CAAEgC,GAAG,CAAE,KAAM,CAAE,CACpB,CAAC,cACF9K,IAAA,CAACnC,GAAG,EACF6M,IAAI,cAAE1K,IAAA,CAAChC,OAAO,EAAC0M,IAAI,CAAC,uBAAuB,CAACC,KAAK,CAAE,EAAG,CAACF,MAAM,CAAE,EAAG,CAAE,CAAE,CACtEG,YAAY,CAAC,OAAO,CACpBC,KAAK,CAAC,YAAY,CAClBf,KAAK,CAAC,YAAY,CAClBhB,EAAE,CAAE,CAAEgC,GAAG,CAAE,KAAM,CAAE,CACpB,CAAC,cACF9K,IAAA,CAACnC,GAAG,EACF6M,IAAI,cAAE1K,IAAA,CAAChC,OAAO,EAAC0M,IAAI,CAAC,uBAAuB,CAACC,KAAK,CAAE,EAAG,CAACF,MAAM,CAAE,EAAG,CAAE,CAAE,CACtEG,YAAY,CAAC,OAAO,CACpBC,KAAK,CAAC,OAAO,CACbf,KAAK,CAAC,OAAO,CACbhB,EAAE,CAAE,CAAEgC,GAAG,CAAE,KAAM,CAAE,CACpB,CAAC,cACF9K,IAAA,CAACnC,GAAG,EACF6M,IAAI,cAAE1K,IAAA,CAAChC,OAAO,EAAC0M,IAAI,CAAC,yBAAyB,CAACC,KAAK,CAAE,EAAG,CAACF,MAAM,CAAE,EAAG,CAAE,CAAE,CACxEG,YAAY,CAAC,OAAO,CACpBC,KAAK,CAAC,SAAS,CACff,KAAK,CAAC,SAAS,CACfhB,EAAE,CAAE,CAAEgC,GAAG,CAAE,KAAM,CAAE,CACpB,CAAC,CACD,CAAAxJ,QAAQ,SAARA,QAAQ,kBAAAhB,qBAAA,CAARgB,QAAQ,CAAEyJ,iBAAiB,UAAAzK,qBAAA,iBAA3BA,qBAAA,CAA6B0D,YAAY,IAAK,OAAO,eACpDhE,IAAA,CAACnC,GAAG,EACF6M,IAAI,cAAE1K,IAAA,CAAChC,OAAO,EAAC0M,IAAI,CAAC,2BAA2B,CAACC,KAAK,CAAE,EAAG,CAACF,MAAM,CAAE,EAAG,CAAE,CAAE,CAC1EG,YAAY,CAAC,OAAO,CACpBC,KAAK,CAAC,QAAQ,CACdf,KAAK,CAAC,QAAQ,CACdhB,EAAE,CAAE,CAAEgC,GAAG,CAAE,KAAM,CAAE,CACpB,CACF,EACG,CAAC,CACJ,CAAC,cAGN5K,KAAA,CAACvC,GAAG,EAAC+K,SAAS,CAAE5I,MAAM,CAACkL,UAAW,CAAApC,QAAA,EAC/BnI,SAAS,GAAK,UAAU,eACvBP,KAAA,CAACvC,GAAG,EAAC+K,SAAS,CAAE5I,MAAM,CAACmL,WAAY,CAACnC,EAAE,CAAE,CAAEgC,GAAG,CAAE,GAAI,CAAE,CAAAlC,QAAA,eACnD5I,IAAA,CAACxB,WAAW,EAAC8C,QAAQ,CAAEA,QAAS,CAAE,CAAC,cACnCpB,KAAA,CAACvC,GAAG,EAACmL,EAAE,CAAE,CAAEoC,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,KAAM,CAAC,CAAEP,GAAG,CAAE,CAAC,CAAEjB,EAAE,CAAE,GAAI,CAAE,CAAAjB,QAAA,eACxF5I,IAAA,CAACvB,UAAU,EAAC+D,KAAK,CAAEA,KAAM,CAAE,CAAC,cAC5BxC,IAAA,CAACtB,QAAQ,EAAC8D,KAAK,CAAEA,KAAM,CAAE,CAAC,EACvB,CAAC,cACNxC,IAAA,CAACrB,OAAO,EACN8C,YAAY,CAAEA,YAAa,CAC3B6J,SAAS,CAAEhK,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEgH,IAAK,CAC1BiD,QAAQ,CAAEA,CAAA,GAAM9I,UAAU,CAAC,QAAQ,CAAE,CACtC,CAAC,cACFzC,IAAA,CAAClB,iBAAiB,EAChBqG,UAAU,CAAE7D,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE6D,UAAW,CACjCjD,0BAA0B,CAAEA,0BAA2B,CACvDC,kBAAkB,CAAEA,kBAAmB,CACvCH,qBAAqB,CAAEA,qBAAsB,CAC7CC,aAAa,CAAEA,aAAc,CAC7BH,wBAAwB,CAAEA,wBAAyB,CACnDC,gBAAgB,CAAEA,gBAAiB,CACpC,CAAC,EACC,CACN,CAEAtB,SAAS,GAAK,YAAY,eACzBT,IAAA,CAACpB,aAAa,EACZuG,UAAU,CAAE7D,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE6D,UAAW,CACjC9C,QAAQ,CAAEA,QAAS,CACnBmJ,OAAO,CAAE,KAAM,CACfC,YAAY,CAAE,IAAK,CACnBvJ,0BAA0B,CAAEA,0BAA2B,CACvDC,kBAAkB,CAAEA,kBAAmB,CACvCH,qBAAqB,CAAEA,qBAAsB,CAC7CC,aAAa,CAAEA,aAAc,CAC7BH,wBAAwB,CAAEA,wBAAyB,CACnDC,gBAAgB,CAAEA,gBAAiB,CACnC2J,iBAAiB,CAAE5G,qBAAsB,CACzC6G,YAAY,CAAEpG,gBAAiB,CAC/BqG,eAAe,CAAE/F,mBAAoB,CACrCgG,SAAS,CAAEtF,aAAc,CACzBuF,YAAY,CAAEhF,gBAAiB,CAC/BiF,YAAY,CAAE7E,gBAAiB,CAC/B8E,eAAe,CAAE3E,mBAAoB,CACrC4E,eAAe,CAAEzE,mBAAoB,CACrC/F,YAAY,CAAE,CAAAH,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE8G,aAAa,GAAI,EAAG,CAC5CkD,SAAS,CAAEhK,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEyG,KAAM,CAC5B,CACF,CAEAtH,SAAS,GAAK,OAAO,eACpBT,IAAA,CAACnB,QAAQ,EACPsG,UAAU,CAAE7D,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE6D,UAAW,CACjCwG,YAAY,CAAEpG,gBAAiB,CAC/BqG,eAAe,CAAE/F,mBAAoB,CACrCiG,YAAY,CAAEhF,gBAAiB,CAC/BiF,YAAY,CAAE7E,gBAAiB,CAC/B8E,eAAe,CAAE3E,mBAAoB,CACtC,CACF,CAEA5G,SAAS,GAAK,SAAS,eACtBT,IAAA,CAACrB,OAAO,EACN8C,YAAY,CAAEA,YAAa,CAC3B6J,SAAS,CAAEhK,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEgH,IAAK,CAC1BiD,QAAQ,CAAEA,CAAA,GAAM9I,UAAU,CAAC,QAAQ,CAAE,CACrCyJ,QAAQ,CAAE,IAAK,CAChB,CACF,CAEAzL,SAAS,GAAK,QAAQ,EAAI,CAAAa,QAAQ,SAARA,QAAQ,kBAAAf,sBAAA,CAARe,QAAQ,CAAEyJ,iBAAiB,UAAAxK,sBAAA,iBAA3BA,sBAAA,CAA6ByD,YAAY,IAAK,OAAO,eAC9EhE,IAAA,CAACjB,gBAAgB,EACfuC,QAAQ,CAAEA,QAAS,CACnB6K,eAAe,CAAE7K,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEyJ,iBAAkB,CAC7CqB,WAAW,CAAE9I,eAAgB,CAC7B+I,cAAc,CAAE/H,kBAAmB,CACnCgI,cAAc,CAAEzH,kBAAmB,CACpC,CACF,EACE,CAAC,EACN,CACH,cAGD7E,IAAA,CAACd,YAAY,EACXqN,IAAI,CAAExL,WAAW,CAACE,MAAO,CACzBuL,OAAO,CAAEA,CAAA,GAAM5J,WAAW,CAAC,QAAQ,CAAE,CACrC2I,QAAQ,CAAE3J,gBAAiB,CAC3BN,QAAQ,CAAEA,QAAS,CACpB,CAAC,cAEFtB,IAAA,CAACb,YAAY,EACXoN,IAAI,CAAExL,WAAW,CAACG,MAAO,CACzBsL,OAAO,CAAEA,CAAA,GAAM5J,WAAW,CAAC,QAAQ,CAAE,CACrC6J,QAAQ,CAAE/K,gBAAiB,CAC5B,CAAC,cAEF1B,IAAA,CAACZ,YAAY,EACXmN,IAAI,CAAExL,WAAW,CAACI,MAAO,CACzBqL,OAAO,CAAEA,CAAA,GAAM5J,WAAW,CAAC,QAAQ,CAAE,CACrC8J,QAAQ,CAAE/K,gBAAiB,CAC5B,CAAC,cAEF3B,IAAA,CAACX,aAAa,EACZkN,IAAI,CAAExL,WAAW,CAACpB,UAAW,CAC7B6M,OAAO,CAAEA,CAAA,GAAMxL,cAAc,CAAC2B,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEhD,UAAU,CAAE,KAAM,CAAC,CAAC,CAAE,CACxEgN,SAAS,CAAEA,CAAA,GAAMzF,gBAAgB,CAAC9F,oBAAoB,CAAE,CACxDwL,KAAK,CAAC,aAAa,CACnBC,WAAW,CAAE,6CAA6CzL,oBAAoB,SAApBA,oBAAoB,iBAApBA,oBAAoB,CAAE0L,IAAI,kCAAmC,CACxH,CAAC,cAEF9M,IAAA,CAACX,aAAa,EACZkN,IAAI,CAAExL,WAAW,CAACnB,aAAc,CAChC4M,OAAO,CAAEA,CAAA,GAAMxL,cAAc,CAAC2B,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE/C,aAAa,CAAE,KAAM,CAAC,CAAC,CAAE,CAC3E+M,SAAS,CAAEA,CAAA,GAAMtF,mBAAmB,CAAChG,uBAAuB,CAAE,CAC9DuL,KAAK,CAAC,gBAAgB,CACtBC,WAAW,CAAE,gDAAgDxL,uBAAuB,SAAvBA,uBAAuB,iBAAvBA,uBAAuB,CAAEyL,IAAI,kCAAmC,CAC9H,CAAC,EACO,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAzM,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}