{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\AccessManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Paper, Button, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Select, MenuItem, FormControl, InputLabel, List, ListItem, ListItemText, ListItemSecondaryAction, IconButton, Chip, Avatar, Tooltip } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AccessManagement = _ref => {\n  _s();\n  let {\n    planInfo,\n    userAccessLevel,\n    onAddAccess,\n    onUpdateAccess,\n    onRemoveAccess\n  } = _ref;\n  const [addDialogOpen, setAddDialogOpen] = useState(false);\n  const [newUserEmail, setNewUserEmail] = useState('');\n  const [newAccessLevel, setNewAccessLevel] = useState('viewer');\n  const [loading, setLoading] = useState(false);\n  const accessLevels = (planInfo === null || planInfo === void 0 ? void 0 : planInfo.access_levels) || [];\n  const isOwner = (userAccessLevel === null || userAccessLevel === void 0 ? void 0 : userAccessLevel.access_level) === 'owner';\n  const isHeadOwner = userAccessLevel === null || userAccessLevel === void 0 ? void 0 : userAccessLevel.is_head_owner;\n  const getAccessLevelColor = level => {\n    switch (level) {\n      case 'owner':\n        return '#ef4444';\n      case 'editor':\n        return '#f97316';\n      case 'viewer':\n        return '#10b981';\n      default:\n        return '#6b7280';\n    }\n  };\n  const getAccessLevelIcon = level => {\n    switch (level) {\n      case 'owner':\n        return 'material-symbols:admin-panel-settings';\n      case 'editor':\n        return 'material-symbols:edit';\n      case 'viewer':\n        return 'material-symbols:visibility';\n      default:\n        return 'material-symbols:person';\n    }\n  };\n  const handleAddAccess = async () => {\n    if (!newUserEmail.trim()) return;\n    setLoading(true);\n    try {\n      await onAddAccess(newUserEmail, newAccessLevel);\n      setNewUserEmail('');\n      setNewAccessLevel('viewer');\n      setAddDialogOpen(false);\n    } catch (error) {\n      console.error('Error adding access:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleUpdateAccess = async function (accessId, newLevel) {\n    let makeHeadOwner = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    try {\n      await onUpdateAccess(accessId, newLevel, makeHeadOwner);\n    } catch (error) {\n      console.error('Error updating access:', error);\n    }\n  };\n  const handleRemoveAccess = async accessId => {\n    try {\n      await onRemoveAccess(accessId);\n    } catch (error) {\n      console.error('Error removing access:', error);\n    }\n  };\n  const canManageUser = access => {\n    if (access.is_head_owner) return false; // Cannot manage head owner\n    if (access.access_level === 'owner' && !isHeadOwner) return false; // Only head owner can manage owners\n    return isOwner; // Owners can manage editors and viewers\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 0,\n    sx: {\n      p: 3,\n      borderRadius: '12px',\n      border: '1px solid #f0f0f0',\n      backgroundColor: '#fff'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 600,\n          color: '#333',\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:security\",\n          width: 24,\n          height: 24,\n          color: mainYellowColor\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), \"Access Management\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), isOwner && /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:person-add\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 24\n        }, this),\n        onClick: () => setAddDialogOpen(true),\n        sx: {\n          backgroundColor: mainYellowColor,\n          color: '#fff',\n          textTransform: 'none',\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          '&:hover': {\n            backgroundColor: '#e6940a'\n          }\n        },\n        children: \"Add Member\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(List, {\n      children: accessLevels.map(access => /*#__PURE__*/_jsxDEV(ListItem, {\n        sx: {\n          border: '1px solid #f0f0f0',\n          borderRadius: '8px',\n          mb: 1,\n          backgroundColor: '#fafafa'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          src: access.user.avatar,\n          alt: access.user.first_name || access.user.email,\n          sx: {\n            mr: 2,\n            bgcolor: mainYellowColor\n          },\n          children: access.user.first_name ? access.user.first_name.charAt(0).toUpperCase() : access.user.email.charAt(0).toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              },\n              children: access.user.first_name && access.user.last_name ? `${access.user.first_name} ${access.user.last_name}` : access.user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 19\n            }, this), access.is_head_owner && /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"Head Owner\",\n              size: \"small\",\n              sx: {\n                backgroundColor: '#fef3c7',\n                color: '#92400e',\n                fontWeight: 600,\n                fontSize: '0.7rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 17\n          }, this),\n          secondary: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: getAccessLevelIcon(access.access_level),\n                width: 16,\n                height: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 27\n              }, this),\n              label: access.access_level.charAt(0).toUpperCase() + access.access_level.slice(1),\n              size: \"small\",\n              sx: {\n                backgroundColor: `${getAccessLevelColor(access.access_level)}20`,\n                color: getAccessLevelColor(access.access_level),\n                fontWeight: 600,\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              },\n              children: access.user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n          children: canManageUser(access) && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 1\n            },\n            children: [access.access_level === 'owner' && isHeadOwner && /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Transfer Head Owner\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => handleUpdateAccess(access.id, 'owner', true),\n                sx: {\n                  color: '#f59e0b'\n                },\n                children: /*#__PURE__*/_jsxDEV(Iconify, {\n                  icon: \"material-symbols:crown\",\n                  width: 20,\n                  height: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Remove Access\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => handleRemoveAccess(access.id),\n                sx: {\n                  color: '#ef4444'\n                },\n                children: /*#__PURE__*/_jsxDEV(Iconify, {\n                  icon: \"material-symbols:person-remove\",\n                  width: 20,\n                  height: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this)]\n      }, access.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addDialogOpen,\n      onClose: () => setAddDialogOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          fontFamily: '\"Recursive Variable\", sans-serif'\n        },\n        children: \"Add Team Member\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          label: \"Email Address\",\n          type: \"email\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newUserEmail,\n          onChange: e => setNewUserEmail(e.target.value),\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Access Level\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: newAccessLevel,\n            label: \"Access Level\",\n            onChange: e => setNewAccessLevel(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"viewer\",\n              children: \"Viewer - Can view plan content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"editor\",\n              children: \"Editor - Can edit plan content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), isHeadOwner && /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"owner\",\n              children: \"Owner - Can manage team members\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setAddDialogOpen(false),\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif'\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleAddAccess,\n          variant: \"contained\",\n          disabled: loading || !newUserEmail.trim(),\n          sx: {\n            backgroundColor: mainYellowColor,\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            '&:hover': {\n              backgroundColor: '#e6940a'\n            }\n          },\n          children: loading ? 'Adding...' : 'Add Member'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n};\n_s(AccessManagement, \"2ZGIN5nYfq5rOP76w4r1NRX6atQ=\");\n_c = AccessManagement;\nexport default AccessManagement;\nvar _c;\n$RefreshReg$(_c, \"AccessManagement\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "Select", "MenuItem", "FormControl", "InputLabel", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "IconButton", "Chip", "Avatar", "<PERSON><PERSON><PERSON>", "Iconify", "mainYellowColor", "jsxDEV", "_jsxDEV", "AccessManagement", "_ref", "_s", "planInfo", "userAccessLevel", "onAddAccess", "onUpdateAccess", "onRemoveAccess", "addDialogOpen", "setAddDialogOpen", "newUserEmail", "setNewUserEmail", "newAccessLevel", "setNewAccessLevel", "loading", "setLoading", "accessLevels", "access_levels", "isOwner", "access_level", "isHeadOwner", "is_head_owner", "getAccessLevelColor", "level", "getAccessLevelIcon", "handleAddAccess", "trim", "error", "console", "handleUpdateAccess", "accessId", "newLevel", "makeHeadOwner", "arguments", "length", "undefined", "handleRemoveAccess", "canManageUser", "access", "elevation", "sx", "p", "borderRadius", "border", "backgroundColor", "children", "display", "justifyContent", "alignItems", "mb", "variant", "fontWeight", "color", "fontFamily", "gap", "icon", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "textTransform", "map", "src", "user", "avatar", "alt", "first_name", "email", "mr", "bgcolor", "char<PERSON>t", "toUpperCase", "primary", "last_name", "label", "size", "fontSize", "secondary", "mt", "slice", "title", "id", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "autoFocus", "margin", "type", "value", "onChange", "e", "target", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/AccessManagement.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  IconButton,\n  Chip,\n  Avatar,\n  Tooltip\n} from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\n\nconst AccessManagement = ({ \n  planInfo, \n  userAccessLevel,\n  onAddAccess,\n  onUpdateAccess,\n  onRemoveAccess \n}) => {\n  const [addDialogOpen, setAddDialogOpen] = useState(false);\n  const [newUserEmail, setNewUserEmail] = useState('');\n  const [newAccessLevel, setNewAccessLevel] = useState('viewer');\n  const [loading, setLoading] = useState(false);\n\n  const accessLevels = planInfo?.access_levels || [];\n  const isOwner = userAccessLevel?.access_level === 'owner';\n  const isHeadOwner = userAccessLevel?.is_head_owner;\n\n  const getAccessLevelColor = (level) => {\n    switch (level) {\n      case 'owner': return '#ef4444';\n      case 'editor': return '#f97316';\n      case 'viewer': return '#10b981';\n      default: return '#6b7280';\n    }\n  };\n\n  const getAccessLevelIcon = (level) => {\n    switch (level) {\n      case 'owner': return 'material-symbols:admin-panel-settings';\n      case 'editor': return 'material-symbols:edit';\n      case 'viewer': return 'material-symbols:visibility';\n      default: return 'material-symbols:person';\n    }\n  };\n\n  const handleAddAccess = async () => {\n    if (!newUserEmail.trim()) return;\n    \n    setLoading(true);\n    try {\n      await onAddAccess(newUserEmail, newAccessLevel);\n      setNewUserEmail('');\n      setNewAccessLevel('viewer');\n      setAddDialogOpen(false);\n    } catch (error) {\n      console.error('Error adding access:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleUpdateAccess = async (accessId, newLevel, makeHeadOwner = false) => {\n    try {\n      await onUpdateAccess(accessId, newLevel, makeHeadOwner);\n    } catch (error) {\n      console.error('Error updating access:', error);\n    }\n  };\n\n  const handleRemoveAccess = async (accessId) => {\n    try {\n      await onRemoveAccess(accessId);\n    } catch (error) {\n      console.error('Error removing access:', error);\n    }\n  };\n\n  const canManageUser = (access) => {\n    if (access.is_head_owner) return false; // Cannot manage head owner\n    if (access.access_level === 'owner' && !isHeadOwner) return false; // Only head owner can manage owners\n    return isOwner; // Owners can manage editors and viewers\n  };\n\n  return (\n    <Paper \n      elevation={0} \n      sx={{ \n        p: 3, \n        borderRadius: '12px',\n        border: '1px solid #f0f0f0',\n        backgroundColor: '#fff'\n      }}\n    >\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography \n          variant=\"h6\" \n          sx={{ \n            fontWeight: 600, \n            color: '#333',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          }}\n        >\n          <Iconify icon=\"material-symbols:security\" width={24} height={24} color={mainYellowColor} />\n          Access Management\n        </Typography>\n        \n        {isOwner && (\n          <Button\n            variant=\"contained\"\n            startIcon={<Iconify icon=\"material-symbols:person-add\" />}\n            onClick={() => setAddDialogOpen(true)}\n            sx={{\n              backgroundColor: mainYellowColor,\n              color: '#fff',\n              textTransform: 'none',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '&:hover': {\n                backgroundColor: '#e6940a'\n              }\n            }}\n          >\n            Add Member\n          </Button>\n        )}\n      </Box>\n\n      <List>\n        {accessLevels.map((access) => (\n          <ListItem \n            key={access.id}\n            sx={{ \n              border: '1px solid #f0f0f0',\n              borderRadius: '8px',\n              mb: 1,\n              backgroundColor: '#fafafa'\n            }}\n          >\n            <Avatar\n              src={access.user.avatar}\n              alt={access.user.first_name || access.user.email}\n              sx={{ mr: 2, bgcolor: mainYellowColor }}\n            >\n              {access.user.first_name \n                ? access.user.first_name.charAt(0).toUpperCase()\n                : access.user.email.charAt(0).toUpperCase()\n              }\n            </Avatar>\n            \n            <ListItemText\n              primary={\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                  <Typography \n                    variant=\"subtitle1\" \n                    sx={{ \n                      fontWeight: 600,\n                      fontFamily: '\"Recursive Variable\", sans-serif'\n                    }}\n                  >\n                    {access.user.first_name && access.user.last_name \n                      ? `${access.user.first_name} ${access.user.last_name}`\n                      : access.user.email\n                    }\n                  </Typography>\n                  {access.is_head_owner && (\n                    <Chip\n                      label=\"Head Owner\"\n                      size=\"small\"\n                      sx={{\n                        backgroundColor: '#fef3c7',\n                        color: '#92400e',\n                        fontWeight: 600,\n                        fontSize: '0.7rem'\n                      }}\n                    />\n                  )}\n                </Box>\n              }\n              secondary={\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>\n                  <Chip\n                    icon={<Iconify icon={getAccessLevelIcon(access.access_level)} width={16} height={16} />}\n                    label={access.access_level.charAt(0).toUpperCase() + access.access_level.slice(1)}\n                    size=\"small\"\n                    sx={{\n                      backgroundColor: `${getAccessLevelColor(access.access_level)}20`,\n                      color: getAccessLevelColor(access.access_level),\n                      fontWeight: 600,\n                      fontFamily: '\"Recursive Variable\", sans-serif'\n                    }}\n                  />\n                  <Typography \n                    variant=\"caption\" \n                    sx={{ \n                      color: '#666',\n                      fontFamily: '\"Recursive Variable\", sans-serif'\n                    }}\n                  >\n                    {access.user.email}\n                  </Typography>\n                </Box>\n              }\n            />\n            \n            <ListItemSecondaryAction>\n              {canManageUser(access) && (\n                <Box sx={{ display: 'flex', gap: 1 }}>\n                  {access.access_level === 'owner' && isHeadOwner && (\n                    <Tooltip title=\"Transfer Head Owner\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => handleUpdateAccess(access.id, 'owner', true)}\n                        sx={{ color: '#f59e0b' }}\n                      >\n                        <Iconify icon=\"material-symbols:crown\" width={20} height={20} />\n                      </IconButton>\n                    </Tooltip>\n                  )}\n                  \n                  <Tooltip title=\"Remove Access\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleRemoveAccess(access.id)}\n                      sx={{ color: '#ef4444' }}\n                    >\n                      <Iconify icon=\"material-symbols:person-remove\" width={20} height={20} />\n                    </IconButton>\n                  </Tooltip>\n                </Box>\n              )}\n            </ListItemSecondaryAction>\n          </ListItem>\n        ))}\n      </List>\n\n      {/* Add Access Dialog */}\n      <Dialog \n        open={addDialogOpen} \n        onClose={() => setAddDialogOpen(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle sx={{ fontFamily: '\"Recursive Variable\", sans-serif' }}>\n          Add Team Member\n        </DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            label=\"Email Address\"\n            type=\"email\"\n            fullWidth\n            variant=\"outlined\"\n            value={newUserEmail}\n            onChange={(e) => setNewUserEmail(e.target.value)}\n            sx={{ mb: 2 }}\n          />\n          \n          <FormControl fullWidth>\n            <InputLabel>Access Level</InputLabel>\n            <Select\n              value={newAccessLevel}\n              label=\"Access Level\"\n              onChange={(e) => setNewAccessLevel(e.target.value)}\n            >\n              <MenuItem value=\"viewer\">Viewer - Can view plan content</MenuItem>\n              <MenuItem value=\"editor\">Editor - Can edit plan content</MenuItem>\n              {isHeadOwner && (\n                <MenuItem value=\"owner\">Owner - Can manage team members</MenuItem>\n              )}\n            </Select>\n          </FormControl>\n        </DialogContent>\n        <DialogActions>\n          <Button \n            onClick={() => setAddDialogOpen(false)}\n            sx={{ fontFamily: '\"Recursive Variable\", sans-serif' }}\n          >\n            Cancel\n          </Button>\n          <Button \n            onClick={handleAddAccess}\n            variant=\"contained\"\n            disabled={loading || !newUserEmail.trim()}\n            sx={{\n              backgroundColor: mainYellowColor,\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '&:hover': { backgroundColor: '#e6940a' }\n            }}\n          >\n            {loading ? 'Adding...' : 'Add Member'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Paper>\n  );\n};\n\nexport default AccessManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,OAAO,QACF,eAAe;AACtB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,gBAAgB,GAAGC,IAAA,IAMnB;EAAAC,EAAA;EAAA,IANoB;IACxBC,QAAQ;IACRC,eAAe;IACfC,WAAW;IACXC,cAAc;IACdC;EACF,CAAC,GAAAN,IAAA;EACC,MAAM,CAACO,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,QAAQ,CAAC;EAC9D,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM0C,YAAY,GAAG,CAAAb,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEc,aAAa,KAAI,EAAE;EAClD,MAAMC,OAAO,GAAG,CAAAd,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEe,YAAY,MAAK,OAAO;EACzD,MAAMC,WAAW,GAAGhB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiB,aAAa;EAElD,MAAMC,mBAAmB,GAAIC,KAAK,IAAK;IACrC,QAAQA,KAAK;MACX,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAID,KAAK,IAAK;IACpC,QAAQA,KAAK;MACX,KAAK,OAAO;QAAE,OAAO,uCAAuC;MAC5D,KAAK,QAAQ;QAAE,OAAO,uBAAuB;MAC7C,KAAK,QAAQ;QAAE,OAAO,6BAA6B;MACnD;QAAS,OAAO,yBAAyB;IAC3C;EACF,CAAC;EAED,MAAME,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACf,YAAY,CAACgB,IAAI,CAAC,CAAC,EAAE;IAE1BX,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMV,WAAW,CAACK,YAAY,EAAEE,cAAc,CAAC;MAC/CD,eAAe,CAAC,EAAE,CAAC;MACnBE,iBAAiB,CAAC,QAAQ,CAAC;MAC3BJ,gBAAgB,CAAC,KAAK,CAAC;IACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,kBAAkB,GAAG,eAAAA,CAAOC,QAAQ,EAAEC,QAAQ,EAA4B;IAAA,IAA1BC,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACzE,IAAI;MACF,MAAM3B,cAAc,CAACwB,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,CAAC;IACzD,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMS,kBAAkB,GAAG,MAAON,QAAQ,IAAK;IAC7C,IAAI;MACF,MAAMvB,cAAc,CAACuB,QAAQ,CAAC;IAChC,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMU,aAAa,GAAIC,MAAM,IAAK;IAChC,IAAIA,MAAM,CAACjB,aAAa,EAAE,OAAO,KAAK,CAAC,CAAC;IACxC,IAAIiB,MAAM,CAACnB,YAAY,KAAK,OAAO,IAAI,CAACC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC;IACnE,OAAOF,OAAO,CAAC,CAAC;EAClB,CAAC;EAED,oBACEnB,OAAA,CAACtB,KAAK;IACJ8D,SAAS,EAAE,CAAE;IACbC,EAAE,EAAE;MACFC,CAAC,EAAE,CAAC;MACJC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,mBAAmB;MAC3BC,eAAe,EAAE;IACnB,CAAE;IAAAC,QAAA,gBAEF9C,OAAA,CAACxB,GAAG;MAACiE,EAAE,EAAE;QAAEM,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF9C,OAAA,CAACvB,UAAU;QACT0E,OAAO,EAAC,IAAI;QACZV,EAAE,EAAE;UACFW,UAAU,EAAE,GAAG;UACfC,KAAK,EAAE,MAAM;UACbC,UAAU,EAAE,kCAAkC;UAC9CP,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBM,GAAG,EAAE;QACP,CAAE;QAAAT,QAAA,gBAEF9C,OAAA,CAACH,OAAO;UAAC2D,IAAI,EAAC,2BAA2B;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE,EAAG;UAACL,KAAK,EAAEvD;QAAgB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAE7F;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZ3C,OAAO,iBACNnB,OAAA,CAACrB,MAAM;QACLwE,OAAO,EAAC,WAAW;QACnBY,SAAS,eAAE/D,OAAA,CAACH,OAAO;UAAC2D,IAAI,EAAC;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1DE,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,IAAI,CAAE;QACtC+B,EAAE,EAAE;UACFI,eAAe,EAAE/C,eAAe;UAChCuD,KAAK,EAAE,MAAM;UACbY,aAAa,EAAE,MAAM;UACrBX,UAAU,EAAE,kCAAkC;UAC9C,SAAS,EAAE;YACTT,eAAe,EAAE;UACnB;QACF,CAAE;QAAAC,QAAA,EACH;MAED;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN9D,OAAA,CAACX,IAAI;MAAAyD,QAAA,EACF7B,YAAY,CAACiD,GAAG,CAAE3B,MAAM,iBACvBvC,OAAA,CAACV,QAAQ;QAEPmD,EAAE,EAAE;UACFG,MAAM,EAAE,mBAAmB;UAC3BD,YAAY,EAAE,KAAK;UACnBO,EAAE,EAAE,CAAC;UACLL,eAAe,EAAE;QACnB,CAAE;QAAAC,QAAA,gBAEF9C,OAAA,CAACL,MAAM;UACLwE,GAAG,EAAE5B,MAAM,CAAC6B,IAAI,CAACC,MAAO;UACxBC,GAAG,EAAE/B,MAAM,CAAC6B,IAAI,CAACG,UAAU,IAAIhC,MAAM,CAAC6B,IAAI,CAACI,KAAM;UACjD/B,EAAE,EAAE;YAAEgC,EAAE,EAAE,CAAC;YAAEC,OAAO,EAAE5E;UAAgB,CAAE;UAAAgD,QAAA,EAEvCP,MAAM,CAAC6B,IAAI,CAACG,UAAU,GACnBhC,MAAM,CAAC6B,IAAI,CAACG,UAAU,CAACI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAC9CrC,MAAM,CAAC6B,IAAI,CAACI,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;QAAC;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEvC,CAAC,eAET9D,OAAA,CAACT,YAAY;UACXsF,OAAO,eACL7E,OAAA,CAACxB,GAAG;YAACiE,EAAE,EAAE;cAAEM,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEM,GAAG,EAAE;YAAE,CAAE;YAAAT,QAAA,gBACzD9C,OAAA,CAACvB,UAAU;cACT0E,OAAO,EAAC,WAAW;cACnBV,EAAE,EAAE;gBACFW,UAAU,EAAE,GAAG;gBACfE,UAAU,EAAE;cACd,CAAE;cAAAR,QAAA,EAEDP,MAAM,CAAC6B,IAAI,CAACG,UAAU,IAAIhC,MAAM,CAAC6B,IAAI,CAACU,SAAS,GAC5C,GAAGvC,MAAM,CAAC6B,IAAI,CAACG,UAAU,IAAIhC,MAAM,CAAC6B,IAAI,CAACU,SAAS,EAAE,GACpDvC,MAAM,CAAC6B,IAAI,CAACI;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CAAC,EACZvB,MAAM,CAACjB,aAAa,iBACnBtB,OAAA,CAACN,IAAI;cACHqF,KAAK,EAAC,YAAY;cAClBC,IAAI,EAAC,OAAO;cACZvC,EAAE,EAAE;gBACFI,eAAe,EAAE,SAAS;gBAC1BQ,KAAK,EAAE,SAAS;gBAChBD,UAAU,EAAE,GAAG;gBACf6B,QAAQ,EAAE;cACZ;YAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;UACDoB,SAAS,eACPlF,OAAA,CAACxB,GAAG;YAACiE,EAAE,EAAE;cAAEM,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEM,GAAG,EAAE,CAAC;cAAE4B,EAAE,EAAE;YAAI,CAAE;YAAArC,QAAA,gBAClE9C,OAAA,CAACN,IAAI;cACH8D,IAAI,eAAExD,OAAA,CAACH,OAAO;gBAAC2D,IAAI,EAAE/B,kBAAkB,CAACc,MAAM,CAACnB,YAAY,CAAE;gBAACqC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxFiB,KAAK,EAAExC,MAAM,CAACnB,YAAY,CAACuD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGrC,MAAM,CAACnB,YAAY,CAACgE,KAAK,CAAC,CAAC,CAAE;cAClFJ,IAAI,EAAC,OAAO;cACZvC,EAAE,EAAE;gBACFI,eAAe,EAAE,GAAGtB,mBAAmB,CAACgB,MAAM,CAACnB,YAAY,CAAC,IAAI;gBAChEiC,KAAK,EAAE9B,mBAAmB,CAACgB,MAAM,CAACnB,YAAY,CAAC;gBAC/CgC,UAAU,EAAE,GAAG;gBACfE,UAAU,EAAE;cACd;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF9D,OAAA,CAACvB,UAAU;cACT0E,OAAO,EAAC,SAAS;cACjBV,EAAE,EAAE;gBACFY,KAAK,EAAE,MAAM;gBACbC,UAAU,EAAE;cACd,CAAE;cAAAR,QAAA,EAEDP,MAAM,CAAC6B,IAAI,CAACI;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEF9D,OAAA,CAACR,uBAAuB;UAAAsD,QAAA,EACrBR,aAAa,CAACC,MAAM,CAAC,iBACpBvC,OAAA,CAACxB,GAAG;YAACiE,EAAE,EAAE;cAAEM,OAAO,EAAE,MAAM;cAAEQ,GAAG,EAAE;YAAE,CAAE;YAAAT,QAAA,GAClCP,MAAM,CAACnB,YAAY,KAAK,OAAO,IAAIC,WAAW,iBAC7CrB,OAAA,CAACJ,OAAO;cAACyF,KAAK,EAAC,qBAAqB;cAAAvC,QAAA,eAClC9C,OAAA,CAACP,UAAU;gBACTuF,IAAI,EAAC,OAAO;gBACZhB,OAAO,EAAEA,CAAA,KAAMlC,kBAAkB,CAACS,MAAM,CAAC+C,EAAE,EAAE,OAAO,EAAE,IAAI,CAAE;gBAC5D7C,EAAE,EAAE;kBAAEY,KAAK,EAAE;gBAAU,CAAE;gBAAAP,QAAA,eAEzB9C,OAAA,CAACH,OAAO;kBAAC2D,IAAI,EAAC,wBAAwB;kBAACC,KAAK,EAAE,EAAG;kBAACC,MAAM,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACV,eAED9D,OAAA,CAACJ,OAAO;cAACyF,KAAK,EAAC,eAAe;cAAAvC,QAAA,eAC5B9C,OAAA,CAACP,UAAU;gBACTuF,IAAI,EAAC,OAAO;gBACZhB,OAAO,EAAEA,CAAA,KAAM3B,kBAAkB,CAACE,MAAM,CAAC+C,EAAE,CAAE;gBAC7C7C,EAAE,EAAE;kBAAEY,KAAK,EAAE;gBAAU,CAAE;gBAAAP,QAAA,eAEzB9C,OAAA,CAACH,OAAO;kBAAC2D,IAAI,EAAC,gCAAgC;kBAACC,KAAK,EAAE,EAAG;kBAACC,MAAM,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACsB,CAAC;MAAA,GApGrBvB,MAAM,CAAC+C,EAAE;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqGN,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP9D,OAAA,CAACpB,MAAM;MACL2G,IAAI,EAAE9E,aAAc;MACpB+E,OAAO,EAAEA,CAAA,KAAM9E,gBAAgB,CAAC,KAAK,CAAE;MACvC+E,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAA5C,QAAA,gBAET9C,OAAA,CAACnB,WAAW;QAAC4D,EAAE,EAAE;UAAEa,UAAU,EAAE;QAAmC,CAAE;QAAAR,QAAA,EAAC;MAErE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACd9D,OAAA,CAAClB,aAAa;QAAAgE,QAAA,gBACZ9C,OAAA,CAAChB,SAAS;UACR2G,SAAS;UACTC,MAAM,EAAC,OAAO;UACdb,KAAK,EAAC,eAAe;UACrBc,IAAI,EAAC,OAAO;UACZH,SAAS;UACTvC,OAAO,EAAC,UAAU;UAClB2C,KAAK,EAAEnF,YAAa;UACpBoF,QAAQ,EAAGC,CAAC,IAAKpF,eAAe,CAACoF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACjDrD,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAEF9D,OAAA,CAACb,WAAW;UAACuG,SAAS;UAAA5C,QAAA,gBACpB9C,OAAA,CAACZ,UAAU;YAAA0D,QAAA,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrC9D,OAAA,CAACf,MAAM;YACL6G,KAAK,EAAEjF,cAAe;YACtBkE,KAAK,EAAC,cAAc;YACpBgB,QAAQ,EAAGC,CAAC,IAAKlF,iBAAiB,CAACkF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAhD,QAAA,gBAEnD9C,OAAA,CAACd,QAAQ;cAAC4G,KAAK,EAAC,QAAQ;cAAAhD,QAAA,EAAC;YAA8B;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClE9D,OAAA,CAACd,QAAQ;cAAC4G,KAAK,EAAC,QAAQ;cAAAhD,QAAA,EAAC;YAA8B;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EACjEzC,WAAW,iBACVrB,OAAA,CAACd,QAAQ;cAAC4G,KAAK,EAAC,OAAO;cAAAhD,QAAA,EAAC;YAA+B;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAClE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAChB9D,OAAA,CAACjB,aAAa;QAAA+D,QAAA,gBACZ9C,OAAA,CAACrB,MAAM;UACLqF,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,KAAK,CAAE;UACvC+B,EAAE,EAAE;YAAEa,UAAU,EAAE;UAAmC,CAAE;UAAAR,QAAA,EACxD;QAED;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9D,OAAA,CAACrB,MAAM;UACLqF,OAAO,EAAEtC,eAAgB;UACzByB,OAAO,EAAC,WAAW;UACnB+C,QAAQ,EAAEnF,OAAO,IAAI,CAACJ,YAAY,CAACgB,IAAI,CAAC,CAAE;UAC1Cc,EAAE,EAAE;YACFI,eAAe,EAAE/C,eAAe;YAChCwD,UAAU,EAAE,kCAAkC;YAC9C,SAAS,EAAE;cAAET,eAAe,EAAE;YAAU;UAC1C,CAAE;UAAAC,QAAA,EAED/B,OAAO,GAAG,WAAW,GAAG;QAAY;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEZ,CAAC;AAAC3D,EAAA,CA/RIF,gBAAgB;AAAAkG,EAAA,GAAhBlG,gBAAgB;AAiStB,eAAeA,gBAAgB;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}